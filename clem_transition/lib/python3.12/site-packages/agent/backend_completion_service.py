"""
Backend Completion Service Module

This module handles sending final completion messages to the backend team queue
after receiving successful completion from the financial pipeline.
"""

import os
import json
import boto3
import hashlib
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from botocore.exceptions import ClientError, BotoCoreError

class BackendCompletionService:
    """Service class for sending completion messages to backend team queue"""
    
    def __init__(self):
        """Initialize backend completion service with placeholder configuration"""
        # TODO: Replace with actual backend team queue details when available
        self.aws_access_key_id = os.getenv("BACKEND_AWS_ACCESS_KEY_ID", os.getenv("SQS_AWS_ACCESS_KEY_ID"))
        self.aws_secret_access_key = os.getenv("BACKEND_AWS_SECRET_ACCESS_KEY", os.getenv("SQS_AWS_SECRET_ACCESS_KEY"))
        self.aws_region = os.getenv("BACKEND_AWS_REGION", os.getenv("AWS_REGION", "ap-south-1"))
        
        # Placeholder queue configuration - will be updated when backend team provides details
        self.queue_url = os.getenv("BACKEND_QUEUE_URL", "PLACEHOLDER_BACKEND_QUEUE_URL")
        self.queue_name = os.getenv("BACKEND_QUEUE_NAME", "backend_completion_queue")
        
        print(f"🔧 Backend Completion Service Configuration:")
        print(f"   - Backend Access Key: {self.aws_access_key_id[:8] + '...' if self.aws_access_key_id else 'NOT SET'}")
        print(f"   - Backend Region: {self.aws_region}")
        print(f"   - Backend Queue URL: {self.queue_url}")
        print(f"   - Status: {'PLACEHOLDER' if 'PLACEHOLDER' in self.queue_url else 'CONFIGURED'}")
        
        # Initialize SQS client if credentials are available
        if self.aws_access_key_id and self.aws_secret_access_key:
            try:
                self.sqs_client = boto3.client(
                    'sqs',
                    aws_access_key_id=self.aws_access_key_id,
                    aws_secret_access_key=self.aws_secret_access_key,
                    region_name=self.aws_region
                )
                print(f"✅ Backend SQS client initialized for region: {self.aws_region}")
            except Exception as e:
                print(f"❌ Failed to initialize backend SQS client: {str(e)}")
                self.sqs_client = None
        else:
            print("⚠️ Backend SQS credentials not configured - using placeholder mode")
            self.sqs_client = None
    
    def create_completion_message(
        self, 
        plant_name: str, 
        extraction_status: str = "Extraction Completed Successfully",
        session_id: str = "unknown",
        additional_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create completion message payload for backend team
        
        Args:
            plant_name: Name of the power plant
            extraction_status: Status message (default: "Extraction Completed Successfully")
            session_id: Session ID for tracking
            additional_data: Optional additional data to include
            
        Returns:
            Dictionary containing formatted completion message
        """
        message_payload = {
            "message_type": "extraction_completion",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "session_id": session_id,
            "data": {
                "power_plant_name": plant_name,
                "status": extraction_status,
                "completion_time": datetime.now(timezone.utc).isoformat(),
                "pipeline_stage": "technical_extraction_complete"
            },
            "metadata": {
                "source": "technical_pipeline",
                "version": "1.0",
                "priority": "normal",
                "queue_name": self.queue_name
            }
        }
        
        # Add additional data if provided
        if additional_data:
            message_payload["data"].update(additional_data)
        
        return message_payload
    
    def send_completion_message(
        self, 
        plant_name: str, 
        extraction_status: str = "Extraction Completed Successfully",
        session_id: str = "unknown",
        additional_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Send completion message to backend team queue
        
        Args:
            plant_name: Name of the power plant
            extraction_status: Status message
            session_id: Session ID for tracking
            additional_data: Optional additional data to include
            
        Returns:
            Dictionary with success status and details
        """
        try:
            # Create message payload
            message_payload = self.create_completion_message(
                plant_name, extraction_status, session_id, additional_data
            )
            
            print(f"[Session {session_id}] 📤 Sending completion message to backend team:")
            print(f"[Session {session_id}]   - Power Plant: {plant_name}")
            print(f"[Session {session_id}]   - Status: {extraction_status}")
            print(f"[Session {session_id}]   - Queue: {self.queue_name}")
            
            # Check if we're in placeholder mode
            if "PLACEHOLDER" in self.queue_url or not self.sqs_client:
                print(f"[Session {session_id}] ⚠️ PLACEHOLDER MODE: Backend queue not configured yet")
                print(f"[Session {session_id}] 📝 Message that would be sent:")
                print(json.dumps(message_payload, indent=2))
                
                return {
                    "success": True,
                    "message_id": f"placeholder_{session_id}_{int(datetime.now().timestamp())}",
                    "mode": "placeholder",
                    "message": "Message logged - backend queue not configured yet",
                    "payload": message_payload,
                    "error": None
                }
            
            # Generate message attributes for SQS
            message_attributes = {
                'MessageType': {
                    'StringValue': 'extraction_completion',
                    'DataType': 'String'
                },
                'SourceSystem': {
                    'StringValue': 'technical_pipeline',
                    'DataType': 'String'
                },
                'PlantName': {
                    'StringValue': plant_name,
                    'DataType': 'String'
                },
                'Status': {
                    'StringValue': extraction_status,
                    'DataType': 'String'
                }
            }
            
            # Send message to backend queue
            response = self.sqs_client.send_message(
                QueueUrl=self.queue_url,
                MessageBody=json.dumps(message_payload, indent=2),
                MessageAttributes=message_attributes
            )
            
            message_id = response.get('MessageId', 'unknown')
            
            print(f"[Session {session_id}] ✅ Completion message sent to backend team!")
            print(f"[Session {session_id}]   - MessageId: {message_id}")
            
            return {
                "success": True,
                "message_id": message_id,
                "mode": "live",
                "timestamp": message_payload["timestamp"],
                "error": None
            }
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            print(f"[Session {session_id}] ❌ AWS SQS ClientError: {error_code} - {error_message}")
            
            return {
                "success": False,
                "message_id": None,
                "error": f"SQS ClientError: {error_code} - {error_message}",
                "error_type": "client_error"
            }
            
        except Exception as e:
            print(f"[Session {session_id}] ❌ Error sending completion message: {str(e)}")
            
            return {
                "success": False,
                "message_id": None,
                "error": f"Unexpected error: {str(e)}",
                "error_type": "unexpected_error"
            }
    
    def test_connection(self, session_id: str = "test") -> bool:
        """
        Test backend queue connection
        
        Args:
            session_id: Session ID for logging
            
        Returns:
            True if connection successful or in placeholder mode, False otherwise
        """
        if "PLACEHOLDER" in self.queue_url or not self.sqs_client:
            print(f"[Session {session_id}] ℹ️ Backend queue in placeholder mode - connection test skipped")
            return True
        
        try:
            # Get queue attributes to test connection
            response = self.sqs_client.get_queue_attributes(
                QueueUrl=self.queue_url,
                AttributeNames=['QueueArn', 'VisibilityTimeout']
            )
            
            queue_arn = response['Attributes'].get('QueueArn', 'unknown')
            print(f"[Session {session_id}] ✅ Backend queue connection test successful!")
            print(f"[Session {session_id}]   - Queue ARN: {queue_arn}")
            
            return True
            
        except Exception as e:
            print(f"[Session {session_id}] ❌ Backend queue connection test failed: {str(e)}")
            return False

# Global backend completion service instance
_backend_completion_service = None

def get_backend_completion_service() -> BackendCompletionService:
    """
    Get or create global backend completion service instance
    
    Returns:
        BackendCompletionService instance
    """
    global _backend_completion_service
    if _backend_completion_service is None:
        _backend_completion_service = BackendCompletionService()
    return _backend_completion_service

def send_backend_completion_message(
    plant_name: str, 
    extraction_status: str = "Extraction Completed Successfully",
    session_id: str = "unknown",
    additional_data: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Convenience function to send completion message to backend team
    
    Args:
        plant_name: Name of the power plant
        extraction_status: Status message
        session_id: Session ID for tracking
        additional_data: Optional additional data to include
        
    Returns:
        Dictionary with success status and details
    """
    try:
        service = get_backend_completion_service()
        return service.send_completion_message(
            plant_name, extraction_status, session_id, additional_data
        )
    except Exception as e:
        print(f"[Session {session_id}] ❌ Failed to get backend completion service: {str(e)}")
        return {
            "success": False,
            "message_id": None,
            "error": f"Service initialization error: {str(e)}",
            "error_type": "service_error"
        }
