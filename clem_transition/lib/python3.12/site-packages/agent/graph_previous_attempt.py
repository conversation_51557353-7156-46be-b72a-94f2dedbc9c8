"""
CLEAN POWER PLANT RESEARCH GRAPH
================================

This is a clean, simplified version of the graph that uses ONLY the clean unit processing system.
All old, complex unit processing functions have been removed.
"""

from typing import TypedDict, Annotated, List, Dict, Any
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage, AIMessage
from langchain_google_genai import ChatGoogleGenerativeAI
import json
import os


class OverallState(TypedDict):
    """Clean state definition for the power plant research system."""
    messages: Annotated[List[BaseMessage], add_messages]
    session_id: str
    search_query: str
    web_research_result: List[str]
    is_sufficient: bool
    follow_up_queries: List[str]
    research_loop_count: int
    number_of_ran_queries: int
    processing_complete: bool


# Import essential functions from original graph backup (keeping only the working ones)
from agent.graph_old_backup import (
    generate_query,
    web_research, 
    reflect_on_research,
    finalize_answer,
    plant_generate_query,
    plant_web_research,
    plant_reflect_on_research,
    plant_finalize_answer,
    detect_multiple_plants,
    route_plant_processing,
    truncate_research_smartly,
    extract_key_information
)


def clean_process_all_units(state: OverallState):
    """
    CLEAN UNIT PROCESSING: Use only clean_unit_processing.py
    
    This replaces ALL old unit processing functions with our clean, simple system.
    """
    from agent.clean_unit_processing import process_units_complete
    
    session_id = state.get("session_id", "unknown")
    print(f"[Session {session_id}] ===== CLEAN UNIT PROCESSING ONLY =====")
    
    # Use the clean unit processing function
    result = process_units_complete(state)
    
    print(f"[Session {session_id}] ===== CLEAN UNIT PROCESSING COMPLETE =====")
    
    return result


# Create the clean graph
clean_builder = StateGraph(OverallState)

# Phase 1: Initial research nodes (keep existing working nodes)
clean_builder.add_node("generate_query", generate_query)
clean_builder.add_node("web_research", web_research)
clean_builder.add_node("reflect_on_research", reflect_on_research)
clean_builder.add_node("finalize_answer", finalize_answer)

# Phase 2: Plant-level research nodes (keep existing working nodes)
clean_builder.add_node("plant_generate_query", plant_generate_query)
clean_builder.add_node("plant_web_research", plant_web_research)
clean_builder.add_node("plant_reflection", plant_reflect_on_research)
clean_builder.add_node("plant_finalize_answer", plant_finalize_answer)
clean_builder.add_node("detect_multiple_plants", detect_multiple_plants)

# Phase 3: Unit processing (ONLY clean processing)
clean_builder.add_node("clean_process_all_units", clean_process_all_units)

# CLEAN GRAPH ROUTING (simple, linear flow)

# Phase 1 Flow
clean_builder.add_edge(START, "generate_query")
clean_builder.add_edge("generate_query", "web_research")
clean_builder.add_edge("web_research", "reflect_on_research")

# Phase 1 routing function (from original graph)
def evaluate_research(state: OverallState) -> str:
    """Simple routing for research evaluation."""
    if state["is_sufficient"] or state.get("research_loop_count", 0) >= 3:
        return "finalize_answer"
    else:
        return "web_research"

clean_builder.add_conditional_edges(
    "reflect_on_research",
    evaluate_research,
    ["web_research", "finalize_answer"]
)

# Phase 2 Flow
clean_builder.add_edge("finalize_answer", "plant_generate_query")
clean_builder.add_edge("plant_generate_query", "plant_web_research")
clean_builder.add_edge("plant_web_research", "plant_reflection")

# Phase 2 routing function (from original graph)
def evaluate_plant_research(state: OverallState) -> str:
    """Simple routing for plant research evaluation."""
    if state["is_sufficient"] or state.get("research_loop_count", 0) >= 3:
        return "plant_finalize_answer"
    else:
        return "plant_web_research"

clean_builder.add_conditional_edges(
    "plant_reflection",
    evaluate_plant_research,
    ["plant_web_research", "plant_finalize_answer"]
)

clean_builder.add_edge("plant_finalize_answer", "detect_multiple_plants")

# Phase 3: Unit Processing (CLEAN AND SIMPLE)
clean_builder.add_conditional_edges(
    "detect_multiple_plants",
    route_plant_processing,
    ["clean_process_all_units"]  # Simplified - only one processing path
)

# Direct to END (no complex routing)
clean_builder.add_edge("clean_process_all_units", END)

# Compile the clean graph
clean_graph = clean_builder.compile()

# Export the clean graph as the main graph
builder = clean_builder
graph = clean_graph

print("🧹 CLEAN GRAPH COMPILED SUCCESSFULLY")
print("✅ All old unit processing functions removed")
print("✅ Only clean_unit_processing.py system active")
print("✅ Simple, linear processing flow")
print("✅ Direct S3 storage integration")
print("🔥 Ready for production use!")