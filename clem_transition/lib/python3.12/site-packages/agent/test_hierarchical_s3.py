#!/usr/bin/env python3
"""
Test script for hierarchical S3 storage implementation.

This script tests the new hierarchical S3 storage structure without requiring
a full workflow run. It validates:
1. Database metadata retrieval
2. S3 path generation
3. File naming with sk values
4. Hierarchical folder structure
"""

import os
import sys
import json
from typing import Dict, Any

# Add the agent directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_metadata():
    """Test database metadata retrieval for S3 path generation."""
    print("🔍 Testing database metadata retrieval...")
    
    try:
        from database_manager import get_database_manager
        
        db_manager = get_database_manager()
        
        # Test with a known plant from the database
        test_plant_name = "Stanwell Power Station"
        metadata = db_manager.get_plant_s3_metadata(test_plant_name)
        
        if metadata:
            print(f"✅ Database metadata retrieved successfully:")
            print(f"   Country: {metadata['country']}")
            print(f"   Org UUID: {metadata['org_uuid']}")
            print(f"   Plant UUID: {metadata['plant_uuid']}")
            print(f"   Org Name: {metadata['org_name']}")
            print(f"   Plant Name: {metadata['plant_name']}")
            return metadata
        else:
            print(f"❌ No metadata found for plant: {test_plant_name}")
            return None
            
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return None

def test_s3_path_generation():
    """Test hierarchical S3 path generation."""
    print("\n🗂️ Testing S3 path generation...")

    try:
        from json_s3_storage import sanitize_filename

        # Test filename sanitization first (this doesn't require database)
        print(f"🧹 Testing filename sanitization:")
        test_filenames = [
            "plant#coal#1",
            "unit#coal#1#plant#1",
            "org<details>",
            "transition/plan",
            "unit#gas#2#plant#3"
        ]

        for filename in test_filenames:
            sanitized = sanitize_filename(filename)
            print(f"   '{filename}' → '{sanitized}'")

        # Test manual path generation using known metadata
        print(f"\n🗂️ Testing manual path generation with known metadata:")

        # Use the metadata we got from the database test
        country = "Australia"
        org_uuid = "ORG_AU_242C25_52576644"
        plant_uuid = "fe3c4e03-a8c1-4861-abf7-7c5d3a92bbf2"

        # Test different data types and sk values
        test_cases = [
            ("organization", "org_details"),
            ("plant", "plant#coal#1"),
            ("unit", "unit#coal#1#plant#1"),
            ("unit", "unit#coal#2#plant#1"),
            ("transition", "transition_plan")
        ]

        for data_type, sk_value in test_cases:
            if data_type == "organization":
                s3_path = f"{country}/{org_uuid}/{org_uuid}.json"
            else:
                sanitized_sk = sanitize_filename(sk_value)
                s3_path = f"{country}/{org_uuid}/{plant_uuid}/{sanitized_sk}.json"

            print(f"✅ {data_type.upper()}: {s3_path}")

    except Exception as e:
        print(f"❌ S3 path generation test failed: {e}")

def test_hierarchical_storage_functions():
    """Test the hierarchical storage functions with sample data."""
    print("\n📤 Testing hierarchical storage functions...")

    # Sample data structures with actual database metadata
    sample_org_data = {
        "sk": "org_details",
        "pk": "ORG_AU_242C25_52576644",
        "organization_name": "Stanwell Corporation Limited",
        "country_name": "Australia",
        "plants_count": 4,
        "plant_types": ["coal"],
        "off_peak_hours": 0.466,
        "peak_hours": 0.9
    }

    sample_plant_data = {
        "sk": "plant#coal#1",
        "pk": "ORG_AU_242C25_52576644",
        "name": "Stanwell Power Station",
        "plant_id": 1,
        "plant_type": "coal",
        "lat": -23.8569,
        "long": 150.2152,
        "plant_address": "Stanwell, Queensland, Australia",
        "units_id": [1, 2, 3, 4]
    }

    sample_unit_data = {
        "sk": "unit#coal#1#plant#1",
        "pk": "ORG_AU_242C25_52576644",
        "unit_number": 1,
        "plant_id": 1,
        "capacity": "350",
        "capacity_unit": "MW",
        "technology": "subcritical",
        "plf": [{"value": 65.2, "year": 2023}]
    }

    sample_transition_data = {
        "sk": "transition_plan",
        "pk": "ORG_AU_242C25_52576644",
        "selected_plan_id": "",
        "transitionPlanStratName": ""
    }

    try:
        from json_s3_storage import sanitize_filename

        # Use known metadata from database test
        country = "Australia"
        org_uuid = "ORG_AU_242C25_52576644"
        plant_uuid = "fe3c4e03-a8c1-4861-abf7-7c5d3a92bbf2"

        # Test organization storage path
        print("📋 Testing organization data structure...")
        org_path = f"{country}/{org_uuid}/{org_uuid}.json"
        print(f"   Organization would be stored at: {org_path}")

        # Test plant storage path
        print("📋 Testing plant data structure...")
        plant_sk_sanitized = sanitize_filename(sample_plant_data["sk"])
        plant_path = f"{country}/{org_uuid}/{plant_uuid}/{plant_sk_sanitized}.json"
        print(f"   Plant would be stored at: {plant_path}")

        # Test unit storage path
        print("📋 Testing unit data structure...")
        unit_sk_sanitized = sanitize_filename(sample_unit_data["sk"])
        unit_path = f"{country}/{org_uuid}/{plant_uuid}/{unit_sk_sanitized}.json"
        print(f"   Unit would be stored at: {unit_path}")

        # Test transition storage path
        print("📋 Testing transition data structure...")
        transition_sk_sanitized = sanitize_filename(sample_transition_data["sk"])
        transition_path = f"{country}/{org_uuid}/{plant_uuid}/{transition_sk_sanitized}.json"
        print(f"   Transition would be stored at: {transition_path}")

        print("\n✅ All storage paths generated successfully!")

    except Exception as e:
        print(f"❌ Hierarchical storage test failed: {e}")

def test_url_structure_generation():
    """Test hierarchical URL structure generation."""
    print("\n🔗 Testing URL structure generation...")

    try:
        from json_s3_storage import sanitize_filename

        # Use known metadata from database test
        country = "Australia"
        org_uuid = "ORG_AU_242C25_52576644"
        plant_uuid = "fe3c4e03-a8c1-4861-abf7-7c5d3a92bbf2"
        bucket = "clem-transition-tech"

        # Generate base URL structure manually
        base_url = f"https://{bucket}.s3.amazonaws.com"
        org_path = f"{country}/{org_uuid}"
        plant_path = f"{org_path}/{plant_uuid}"

        print("✅ Base URL structure generated:")
        print(f"   Plant Name: Stanwell Power Station")
        print(f"   Base URL: {base_url}")
        print(f"   Organization URL: {base_url}/{org_path}/{org_uuid}.json")
        print(f"   Plant Folder: {plant_path}")

        # Test updating with sk values
        test_sk_values = [
            ("plant", "plant#coal#1"),
            ("unit", "unit#coal#1#plant#1"),
            ("unit", "unit#coal#2#plant#1"),
            ("transition", "transition_plan")
        ]

        print("✅ Updated URL structure with sk values:")
        for data_type, sk_value in test_sk_values:
            sanitized_sk = sanitize_filename(sk_value)
            if data_type == "plant":
                url = f"{base_url}/{plant_path}/{sanitized_sk}.json"
                print(f"   Plant URL: {url}")
            elif data_type == "unit":
                url = f"{base_url}/{plant_path}/{sanitized_sk}.json"
                unit_num = sk_value.split('#')[2]  # Extract unit number
                print(f"   Unit {unit_num} URL: {url}")
            elif data_type == "transition":
                url = f"{base_url}/{plant_path}/{sanitized_sk}.json"
                print(f"   Transition URL: {url}")

    except Exception as e:
        print(f"❌ URL structure test failed: {e}")

def main():
    """Run all tests."""
    print("🧪 Testing Hierarchical S3 Storage Implementation")
    print("=" * 60)
    
    # Test database connectivity and metadata retrieval
    metadata = test_database_metadata()
    
    if metadata:
        # Test S3 path generation
        test_s3_path_generation()
        
        # Test storage functions
        test_hierarchical_storage_functions()
        
        # Test URL structure generation
        test_url_structure_generation()
        
        print("\n✅ All tests completed!")
        print("\n📋 Summary of new hierarchical structure:")
        print(f"   Bucket: clem_transition_tech")
        print(f"   Structure: {{country}}/{{org_uuid}}/{{org_uuid}}.json")
        print(f"              {{country}}/{{org_uuid}}/{{plant_uuid}}/{{sk}}.json")
        print(f"   Example: India/ORG_IN_657FE5_51516770/ORG_IN_657FE5_51516770.json")
        print(f"            India/ORG_IN_657FE5_51516770/550e8400.../plant#coal#1.json")
    else:
        print("\n❌ Cannot proceed with tests - database metadata retrieval failed")
        print("   Make sure you have plant data in your database")

if __name__ == "__main__":
    main()
