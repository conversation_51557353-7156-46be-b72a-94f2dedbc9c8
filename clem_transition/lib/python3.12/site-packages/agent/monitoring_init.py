"""
SQS Completion Message Monitoring Initialization

This module provides initialization functions for the SQS completion message
monitoring system that can be called independently of FastAPI lifespan events.
"""

import atexit
from typing import Optional

# Global monitoring state
_monitoring_started = False
_monitoring_service = None

def initialize_completion_monitoring(check_interval: int = 30, auto_start: bool = True) -> bool:
    """
    Initialize SQS completion message monitoring
    
    This function can be called at any time to start the monitoring service.
    It's designed to be safe to call multiple times.
    
    Args:
        check_interval: How often to check for messages (seconds)
        auto_start: Whether to automatically start if SQS is configured
        
    Returns:
        True if monitoring was started, False otherwise
    """
    global _monitoring_started, _monitoring_service
    
    if _monitoring_started:
        print("ℹ️ SQS completion monitoring is already running")
        return True
    
    try:
        if auto_start:
            from agent.completion_monitor_service import auto_start_monitoring_if_configured
            
            print("🚀 Initializing SQS completion message monitoring...")
            
            if auto_start_monitoring_if_configured():
                _monitoring_started = True
                print("✅ SQS completion monitoring started automatically")
                
                # Register cleanup function
                atexit.register(cleanup_completion_monitoring)
                
                return True
            else:
                print("ℹ️ SQS completion monitoring not started (configuration missing)")
                return False
        else:
            from agent.completion_monitor_service import start_completion_monitoring
            
            print(f"🚀 Starting SQS completion monitoring with {check_interval}s interval...")
            
            if start_completion_monitoring(check_interval):
                _monitoring_started = True
                print("✅ SQS completion monitoring started")
                
                # Register cleanup function
                atexit.register(cleanup_completion_monitoring)
                
                return True
            else:
                print("❌ Failed to start SQS completion monitoring")
                return False
                
    except Exception as e:
        print(f"⚠️ Error initializing completion monitoring: {str(e)}")
        return False

def cleanup_completion_monitoring():
    """
    Clean up completion monitoring on application exit
    
    This function is automatically registered with atexit when monitoring starts.
    """
    global _monitoring_started
    
    if not _monitoring_started:
        return
    
    try:
        from agent.completion_monitor_service import stop_completion_monitoring
        
        print("🛑 Cleaning up SQS completion monitoring...")
        
        if stop_completion_monitoring():
            print("✅ SQS completion monitoring stopped gracefully")
        else:
            print("ℹ️ SQS completion monitoring was not running")
            
        _monitoring_started = False
        
    except Exception as e:
        print(f"⚠️ Error stopping completion monitoring: {str(e)}")

def get_monitoring_status() -> dict:
    """
    Get current monitoring status
    
    Returns:
        Dictionary with monitoring status information
    """
    global _monitoring_started
    
    try:
        if _monitoring_started:
            from agent.completion_monitor_service import get_monitoring_status
            return {
                "initialized": True,
                "service_status": get_monitoring_status()
            }
        else:
            return {
                "initialized": False,
                "service_status": None
            }
    except Exception as e:
        return {
            "initialized": _monitoring_started,
            "service_status": None,
            "error": str(e)
        }

def is_monitoring_running() -> bool:
    """
    Check if monitoring is currently running
    
    Returns:
        True if monitoring is running, False otherwise
    """
    global _monitoring_started
    
    if not _monitoring_started:
        return False
    
    try:
        from agent.completion_monitor_service import get_monitoring_status
        status = get_monitoring_status()
        return status.get("running", False)
    except Exception:
        return False

def restart_monitoring(check_interval: int = 30) -> bool:
    """
    Restart the monitoring service
    
    Args:
        check_interval: How often to check for messages (seconds)
        
    Returns:
        True if restart was successful, False otherwise
    """
    print("🔄 Restarting SQS completion monitoring...")
    
    # Stop current monitoring
    cleanup_completion_monitoring()
    
    # Start new monitoring
    return initialize_completion_monitoring(check_interval, auto_start=False)

# Convenience function for manual initialization
def start_monitoring_now(check_interval: int = 30) -> bool:
    """
    Immediately start monitoring (convenience function)
    
    Args:
        check_interval: How often to check for messages (seconds)
        
    Returns:
        True if started successfully, False otherwise
    """
    return initialize_completion_monitoring(check_interval, auto_start=False)

# Auto-initialization on module import (optional)
def auto_initialize_on_import():
    """
    Automatically initialize monitoring when this module is imported
    
    This is called at the bottom of this file to provide automatic initialization.
    """
    try:
        # Only auto-initialize if SQS environment variables are set
        import os
        
        required_vars = [
            "SQS_AWS_ACCESS_KEY_ID",
            "SQS_AWS_SECRET_ACCESS_KEY", 
            "SQS_QUEUE_URL"
        ]
        
        if all(os.getenv(var) for var in required_vars):
            print("🔍 SQS environment detected, auto-initializing completion monitoring...")
            initialize_completion_monitoring()
        else:
            print("ℹ️ SQS environment not configured, skipping auto-initialization")
            
    except Exception as e:
        print(f"⚠️ Error in auto-initialization: {str(e)}")

# Uncomment the line below to enable auto-initialization on import
# auto_initialize_on_import()
