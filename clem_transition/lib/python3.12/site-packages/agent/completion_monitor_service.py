"""
Continuous SQS Completion Message Monitor Service

This service runs continuously in the background to automatically
monitor and process completion messages from the financial pipeline.
"""

import time
import threading
import signal
import sys
from typing import Dict, Any, Optional
from datetime import datetime, timezone

from .completion_handler import get_completion_handler

class CompletionMonitorService:
    """
    Continuous background service for monitoring SQS completion messages
    
    This service:
    1. Runs continuously in a background thread
    2. Polls SQS queue every 30 seconds
    3. Automatically processes completion messages when received
    4. Handles graceful shutdown
    5. Provides status monitoring
    """
    
    def __init__(self, check_interval: int = 30):
        """
        Initialize the completion monitor service
        
        Args:
            check_interval: How often to check for messages (seconds)
        """
        self.check_interval = check_interval
        self.running = False
        self.thread = None
        self.completion_handler = get_completion_handler()
        self.stats = {
            "start_time": None,
            "total_checks": 0,
            "total_messages_processed": 0,
            "last_check_time": None,
            "last_message_time": None,
            "errors": 0
        }
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def start(self) -> bool:
        """
        Start the continuous monitoring service
        
        Returns:
            True if started successfully, False otherwise
        """
        if self.running:
            print("⚠️ Completion monitor service is already running")
            return False
        
        print("🚀 Starting continuous SQS completion message monitor...")
        print(f"   - Check interval: {self.check_interval} seconds")
        print(f"   - Service will run continuously until stopped")
        
        self.running = True
        self.stats["start_time"] = datetime.now(timezone.utc)
        
        # Start monitoring in background thread
        self.thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.thread.start()
        
        print("✅ Completion monitor service started successfully")
        print("   - Running in background thread")
        print("   - Use stop() to gracefully shutdown")
        print("   - Use get_status() to check service status")
        
        return True
    
    def stop(self) -> bool:
        """
        Stop the continuous monitoring service
        
        Returns:
            True if stopped successfully, False otherwise
        """
        if not self.running:
            print("⚠️ Completion monitor service is not running")
            return False
        
        print("🛑 Stopping completion monitor service...")
        self.running = False
        
        # Wait for thread to finish (with timeout)
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=10)
            
            if self.thread.is_alive():
                print("⚠️ Service thread did not stop gracefully")
                return False
        
        print("✅ Completion monitor service stopped successfully")
        self._print_final_stats()
        
        return True
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get current service status and statistics
        
        Returns:
            Dictionary with service status and stats
        """
        uptime = None
        if self.stats["start_time"]:
            uptime = (datetime.now(timezone.utc) - self.stats["start_time"]).total_seconds()
        
        return {
            "running": self.running,
            "check_interval": self.check_interval,
            "uptime_seconds": uptime,
            "stats": self.stats.copy(),
            "thread_alive": self.thread.is_alive() if self.thread else False
        }
    
    def _monitor_loop(self):
        """
        Main monitoring loop that runs continuously
        """
        print("👁️ Completion monitor loop started")
        
        while self.running:
            try:
                self._check_and_process_messages()
                
                # Sleep for check interval (but check running status frequently)
                sleep_time = 0
                while sleep_time < self.check_interval and self.running:
                    time.sleep(1)
                    sleep_time += 1
                    
            except Exception as e:
                self.stats["errors"] += 1
                print(f"❌ Error in monitor loop: {str(e)}")
                
                # Sleep before retrying
                time.sleep(min(self.check_interval, 60))
        
        print("👁️ Completion monitor loop stopped")
    
    def _check_and_process_messages(self):
        """
        Check for and process completion messages
        """
        session_id = f"monitor_{int(time.time())}"
        self.stats["total_checks"] += 1
        self.stats["last_check_time"] = datetime.now(timezone.utc)
        
        try:
            # Process all available completion messages
            result = self.completion_handler.process_all_completion_messages(session_id)
            
            if result.get("messages_processed", 0) > 0:
                messages_processed = result["messages_processed"]
                self.stats["total_messages_processed"] += messages_processed
                self.stats["last_message_time"] = datetime.now(timezone.utc)
                
                print(f"[Monitor] 📨 Processed {messages_processed} completion messages")
                print(f"[Monitor] 📊 Total processed: {self.stats['total_messages_processed']}")
            
            # Log periodic status (every 10 checks)
            if self.stats["total_checks"] % 10 == 0:
                uptime = (datetime.now(timezone.utc) - self.stats["start_time"]).total_seconds()
                print(f"[Monitor] ⏱️ Status: {self.stats['total_checks']} checks, "
                      f"{self.stats['total_messages_processed']} messages, "
                      f"{uptime:.0f}s uptime")
                
        except Exception as e:
            self.stats["errors"] += 1
            print(f"[Monitor] ❌ Error checking messages: {str(e)}")
    
    def _signal_handler(self, signum, frame):
        """
        Handle shutdown signals gracefully
        """
        print(f"\n🛑 Received signal {signum}, shutting down completion monitor...")
        self.stop()
        sys.exit(0)
    
    def _print_final_stats(self):
        """
        Print final statistics when service stops
        """
        if self.stats["start_time"]:
            uptime = (datetime.now(timezone.utc) - self.stats["start_time"]).total_seconds()
            
            print("📊 Final Service Statistics:")
            print(f"   - Uptime: {uptime:.0f} seconds ({uptime/3600:.1f} hours)")
            print(f"   - Total checks: {self.stats['total_checks']}")
            print(f"   - Messages processed: {self.stats['total_messages_processed']}")
            print(f"   - Errors: {self.stats['errors']}")
            
            if self.stats["last_message_time"]:
                print(f"   - Last message: {self.stats['last_message_time'].strftime('%Y-%m-%d %H:%M:%S UTC')}")

# Global service instance
_monitor_service = None

def get_completion_monitor_service() -> CompletionMonitorService:
    """
    Get or create global completion monitor service instance
    
    Returns:
        CompletionMonitorService instance
    """
    global _monitor_service
    if _monitor_service is None:
        _monitor_service = CompletionMonitorService()
    return _monitor_service

def start_completion_monitoring(check_interval: int = 30) -> bool:
    """
    Start continuous completion message monitoring
    
    Args:
        check_interval: How often to check for messages (seconds)
        
    Returns:
        True if started successfully, False otherwise
    """
    service = get_completion_monitor_service()
    service.check_interval = check_interval
    return service.start()

def stop_completion_monitoring() -> bool:
    """
    Stop continuous completion message monitoring
    
    Returns:
        True if stopped successfully, False otherwise
    """
    service = get_completion_monitor_service()
    return service.stop()

def get_monitoring_status() -> Dict[str, Any]:
    """
    Get current monitoring service status
    
    Returns:
        Dictionary with service status and stats
    """
    service = get_completion_monitor_service()
    return service.get_status()

# Convenience functions for easy integration
def auto_start_monitoring_if_configured() -> bool:
    """
    Automatically start monitoring if environment is configured
    
    Returns:
        True if monitoring started or already running, False if not configured
    """
    try:
        # Check if SQS is configured
        from .sqs_service import get_sqs_service
        sqs_service = get_sqs_service()
        
        if sqs_service.test_connection("auto_start"):
            print("✅ SQS configured, starting automatic completion monitoring...")
            return start_completion_monitoring()
        else:
            print("⚠️ SQS not configured, skipping automatic completion monitoring")
            return False
            
    except Exception as e:
        print(f"❌ Error checking SQS configuration: {str(e)}")
        return False
