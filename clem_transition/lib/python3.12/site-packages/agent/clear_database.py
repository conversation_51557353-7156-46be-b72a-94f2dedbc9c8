"""
Clear the entire plant registry database
"""

from database_manager import get_database_manager, PowerPlantRegistry

def clear_database():
    """Clear all data from the plant registry database"""
    
    print("🗑️ CLEARING PLANT REGISTRY DATABASE")
    print("=" * 50)
    
    try:
        db_manager = get_database_manager()
        session = db_manager.get_session()
        
        # Count records before deletion
        count_before = session.query(PowerPlantRegistry).count()
        print(f"📊 Records before deletion: {count_before}")
        
        if count_before == 0:
            print("✅ Database is already empty!")
            return True
        
        # Delete all records
        deleted_count = session.query(PowerPlantRegistry).delete()
        session.commit()
        
        # Verify deletion
        count_after = session.query(PowerPlantRegistry).count()
        
        print(f"🗑️ Records deleted: {deleted_count}")
        print(f"📊 Records after deletion: {count_after}")
        
        if count_after == 0:
            print("✅ Database cleared successfully!")
            return True
        else:
            print("❌ Database clearing failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error clearing database: {e}")
        session.rollback()
        return False
    finally:
        session.close()

def verify_empty_database():
    """Verify that the database is empty"""
    
    print("\n🔍 VERIFYING EMPTY DATABASE")
    print("=" * 30)
    
    try:
        db_manager = get_database_manager()
        session = db_manager.get_session()
        
        count = session.query(PowerPlantRegistry).count()
        
        if count == 0:
            print("✅ Database is confirmed empty!")
            print("📊 Total records: 0")
            print("🏢 Organizations: 0")
            print("🔑 UIDs: 0")
            return True
        else:
            print(f"❌ Database still has {count} records!")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying database: {e}")
        return False
    finally:
        session.close()

def main():
    """Main function to clear and verify database"""
    
    # Clear the database
    success = clear_database()
    
    if success:
        # Verify it's empty
        verify_empty_database()
        print("\n🎉 DATABASE CLEARING COMPLETE!")
        print("Ready for fresh data population.")
    else:
        print("\n❌ DATABASE CLEARING FAILED!")
        return False
    
    return True

if __name__ == "__main__":
    main()