import requests
from bs4 import BeautifulSoup
import os
import certifi
from urllib.parse import urljoin, urlparse
import mediapipe as mp
import cv2
import easyocr
import boto3
from dotenv import load_dotenv

load_dotenv()

# ========== CONFIG ==========
# Use S3-specific credentials for image storage
AWS_ACCESS_KEY = os.getenv('S3_AWS_ACCESS_KEY_ID')
AWS_SECRET_KEY = os.getenv('S3_AWS_SECRET_ACCESS_KEY')
AWS_REGION = os.getenv('AWS_REGION', 'ap-south-1')
S3_BUCKET = 'clem-transition-tech'  # Use the correct S3 bucket
SCRAPER_API_KEY = os.getenv('SCRAPER_API_KEY')

# Debug logging for S3 configuration
print(f"🔧 Image Extraction S3 Configuration:")
print(f"   - S3 Access Key: {AWS_ACCESS_KEY[:8] + '...' if AWS_ACCESS_KEY else 'NOT SET'}")
print(f"   - S3 Region: {AWS_REGION}")
print(f"   - S3 Bucket: {S3_BUCKET}")
# ============================

def search_google(query: str, page: int, num_results=5):
    payload = {'api_key': SCRAPER_API_KEY, 'query': query, 'page': page, 'num': num_results}
    try:
        response = requests.get('https://api.scraperapi.com/structured/google/search', params=payload, verify=certifi.where())
        response.raise_for_status()
        results = response.json()
        links = []
        for result in results.get('organic_results', []):
            link = result.get('link', '')
            if link:
                links.append(link)
        return links
    except Exception as e:
        print(f"Error during Google search: {e}")
        return []

def get_internal_links(base_url, url, max_links=5):
    EXCLUDED_EXTENSIONS = ('.pdf', '.jpg', '.jpeg', '.png', '.gif', '.svg', '.doc', '.docx',
        '.xls', '.xlsx', '.ppt', '.pptx', '.zip', '.rar', '.tar.gz', '.mp4', '.mp3', '.wav')
    try:
        response = requests.get(url)
        LINKS = []
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, "html.parser")
            internal_links = []
            for a in soup.find_all('a', href=True):
                href = a['href'].split('?')[0].split('#')[0].strip()
                full_url = urljoin(base_url, href)
                if full_url.startswith(base_url) and not href.lower().endswith(EXCLUDED_EXTENSIONS):
                    if full_url not in internal_links:
                        internal_links.append(full_url)
                        if len(internal_links) >= max_links:
                            break
            for link in internal_links:
                LINKS.append(link)
        return LINKS
    except Exception as e:
        print(f"Exception in get_internal_links for {url}: {e}")
        return []

def get_images(base_url, LINKS, image_folder):
    if len(LINKS) > 0:
        downloaded_images = set()
        os.makedirs(image_folder, exist_ok=True)
        for link in LINKS:
            try:
                response = requests.get(link)
            except Exception as e:
                continue
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, "html.parser")
                img_tags = soup.find_all('img')
                for img in img_tags:
                    img_url = img.get('src')
                    if not img_url:
                        continue
                    img_url = urljoin(base_url, img_url.split('?')[0].split('#')[0])
                    filename = os.path.basename(urlparse(img_url).path)
                    if not filename:
                        continue
                    filepath = os.path.join(image_folder, filename)
                    if img_url in downloaded_images:
                        continue
                    downloaded_images.add(img_url)
                    try:
                        img_resp = requests.get(img_url, timeout=10)
                        img_resp.raise_for_status()
                        with open(filepath, 'wb') as f:
                            f.write(img_resp.content)
                    except Exception:
                        continue

def face_and_text_filter(image_folder):
    # Face removal
    mp_face = mp.solutions.face_detection
    image_files = os.listdir(image_folder)
    with mp_face.FaceDetection(model_selection=1, min_detection_confidence=0.5) as face_detection:
        for image_link in image_files:
            image_path = os.path.join(image_folder, image_link)
            img = cv2.imread(image_path)
            if img is None:
                continue
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            results = face_detection.process(img_rgb)
            if results.detections:
                os.remove(image_path)
    # Text removal
    reader = easyocr.Reader(['en'])
    image_files = os.listdir(image_folder)
    for image_link in image_files:
        image_path = os.path.join(image_folder, image_link)
        img = cv2.imread(image_path)
        if img is None:
            continue
        results = reader.readtext(image_path)
        if results:
            os.remove(image_path)

def remove_low_quality(image_folder):
    VARIANCE_THRESHOLD = 100
    MIN_RESOLUTION = 300
    for filename in os.listdir(image_folder):
        filepath = os.path.join(image_folder, filename)
        if os.path.isfile(filepath):
            image = cv2.imread(filepath)
            if image is None:
                os.remove(filepath)
                continue
            h, w = image.shape[:2]
            if w < MIN_RESOLUTION or h < MIN_RESOLUTION:
                os.remove(filepath)
                continue
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            variance = cv2.Laplacian(gray, cv2.CV_64F).var()
            if variance < VARIANCE_THRESHOLD:
                os.remove(filepath)

def upload_folder_to_s3(local_folder, bucket_name, aws_access_key, aws_secret_key, aws_region="us-east-1", s3_prefix=None, max_images=5):
    """
    Uploads files from a local folder to S3 bucket with a maximum limit.
    
    Args:
        max_images (int): Maximum number of images to upload (default: 5)
    """
    s3 = boto3.client(
        's3',
        aws_access_key_id=aws_access_key,
        aws_secret_access_key=aws_secret_key,
        region_name=aws_region
    )
    s3_links = []
    uploaded_count = 0
    
    # Collect all files first
    all_files = []
    for root, dirs, files in os.walk(local_folder):
        for file in files:
            local_path = os.path.join(root, file)
            all_files.append(local_path)
    
    # Sort files to ensure consistent selection (optional: by size, name, etc.)
    all_files.sort()
    
    # Upload only up to max_images
    for local_path in all_files:
        if uploaded_count >= max_images:
            print(f"⚠️  Reached maximum limit of {max_images} images. Skipping remaining {len(all_files) - uploaded_count} images.")
            break
            
        relative_path = os.path.relpath(local_path, local_folder)
        s3_key = os.path.join(s3_prefix, relative_path) if s3_prefix else relative_path
        s3_key = s3_key.replace("\\", "/")  # Windows fix
        
        try:
            s3.upload_file(local_path, bucket_name, s3_key)
            s3_url = f"https://{bucket_name}.s3.amazonaws.com/{s3_key}"
            s3_links.append(s3_url)
            uploaded_count += 1
            print(f"✅ Uploaded ({uploaded_count}/{max_images}): {os.path.basename(local_path)}")
        except Exception as e:
            print(f"❌ Failed to upload {os.path.basename(local_path)}: {e}")
    
    print(f"🎉 Successfully uploaded {uploaded_count} out of {len(all_files)} total images to S3")
    return s3_links

def extract_and_upload_images(powerplant: str, session_id: str = "unknown") -> list:
    """
    Extract images for a power plant and upload to S3.
    
    Args:
        powerplant (str): Name of the power plant
        session_id (str): Session ID for logging
        
    Returns:
        list: List of S3 URLs for uploaded images
    """
    print(f"[Session {session_id}] 🖼️ Starting image extraction for: {powerplant}")
    
    try:
        query = powerplant
        page = 1

        # Search & download logic
        result_urls = search_google(query, page, num_results=5)
        if not result_urls:
            print(f"[Session {session_id}] ❌ No search results found for {powerplant}")
            return []
            
        all_links_to_scrape = []
        first_url = result_urls[0]
        first_parsed = urlparse(first_url)
        is_first_wikipedia = 'wikipedia.org' in first_parsed.netloc

        image_folder = f"{powerplant.replace(' ', '_')}_images_{session_id}"

        if is_first_wikipedia:
            all_links_to_scrape.append(first_url)
            count = 0
            idx = 1
            while count < 2 and idx < len(result_urls):
                next_url = result_urls[idx]
                next_parsed = urlparse(next_url)
                if 'wikipedia.org' not in next_parsed.netloc:
                    base_url = f"{next_parsed.scheme}://{next_parsed.netloc}"
                    links = [next_url]
                    internal_links = get_internal_links(base_url, next_url, max_links=5)
                    links.extend(internal_links)
                    all_links_to_scrape.extend(links)
                    count += 1
                idx += 1
        else:
            for url in result_urls[:3]:
                parsed_url = urlparse(url)
                base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
                links = [url]
                internal_links = get_internal_links(base_url, url, max_links=5)
                links.extend(internal_links)
                all_links_to_scrape.extend(links)

        all_links_to_scrape = list(set(all_links_to_scrape))
        print(f"[Session {session_id}] 🔍 Found {len(all_links_to_scrape)} links to scrape for images")
        
        for link in all_links_to_scrape:
            parsed_url = urlparse(link)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
            get_images(base_url, [link], image_folder)

        # Filtering
        if os.path.exists(image_folder):
            print(f"[Session {session_id}] 🔧 Filtering images (removing faces, text, low quality)")
            face_and_text_filter(image_folder)
            remove_low_quality(image_folder)

        # Upload to S3 and get URLs (maximum 5 images)
        s3_links = upload_folder_to_s3(
            local_folder=image_folder,
            bucket_name=S3_BUCKET,
            aws_access_key=AWS_ACCESS_KEY,
            aws_secret_key=AWS_SECRET_KEY,
            aws_region=AWS_REGION,
            s3_prefix=f"{image_folder}_{session_id}",
            max_images=5  # Limit to 5 images maximum
        )

        print(f"[Session {session_id}] ✅ Successfully uploaded {len(s3_links)} images for {powerplant}")
        
        # Clean up local folder after upload
        try:
            import shutil
            if os.path.exists(image_folder):
                shutil.rmtree(image_folder)
                print(f"[Session {session_id}] 🧹 Cleaned up local image folder")
        except Exception as e:
            print(f"[Session {session_id}] ⚠️ Could not clean up folder: {e}")
        
        return s3_links
        
    except Exception as e:
        print(f"[Session {session_id}] ❌ Error in image extraction: {e}")
        return []

def main():
    """CLI interface for standalone usage"""
    powerplant = input("Enter the power plant name: ").strip()
    s3_links = extract_and_upload_images(powerplant, "cli")
    
    print(f"Uploaded {len(s3_links)} images. S3 links:")
    for url in s3_links:
        print(url)
    return s3_links

if __name__ == "__main__":
    main()
