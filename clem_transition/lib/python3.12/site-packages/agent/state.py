from __future__ import annotations

from dataclasses import dataclass, field
from typing import TypedDict
try:
    from typing import NotRequired
except ImportError:
    from typing_extensions import NotRequired

from langgraph.graph import add_messages
from typing_extensions import Annotated


import operator
from dataclasses import dataclass, field
from typing_extensions import Annotated


def accumulate_within_phase(existing: list, new: list) -> list:
    """Custom accumulator that only accumulates within the same research phase/session"""
    if existing is None:
        return new
    return existing + new

def last_value_wins(existing, new):
    """Reducer that returns the last (most recent) value - used for fields that should not accumulate"""
    return new

def merge_unit_matrices(existing: dict, updates: dict) -> dict:
    """Custom reducer for unit state matrices - merges unit-specific updates without conflicts"""
    if existing is None:
        existing = {}
    
    result = existing.copy()
    
    # Handle direct dict updates
    if isinstance(updates, dict):
        for unit_id, unit_data in updates.items():
            if unit_id not in result:
                result[unit_id] = {}
            if isinstance(unit_data, dict):
                result[unit_id].update(unit_data)
            else:
                result[unit_id] = unit_data
    
    return result

def merge_unit_flags(existing: dict, updates: dict) -> dict:
    """Custom reducer for unit flags - allows concurrent updates to different unit flags"""
    if existing is None:
        existing = {}
    
    result = existing.copy()
    if isinstance(updates, dict):
        result.update(updates)
    
    return result

class OverallState(TypedDict):
    messages: Annotated[list, add_messages]
    search_query: Annotated[list, accumulate_within_phase]
    web_research_result: Annotated[list, accumulate_within_phase] 
    sources_gathered: Annotated[list, accumulate_within_phase]
    initial_search_query_count: Annotated[int, last_value_wins]
    max_research_loops: Annotated[int, last_value_wins]
    research_loop_count: Annotated[int, last_value_wins]
    reasoning_model: Annotated[str, last_value_wins]
    search_phase: Annotated[int, last_value_wins]  # 1 for organization-level, 2 for plant-level, 3 for unit-level
    continue_research: Annotated[bool, last_value_wins]  # Flag to indicate if we should continue to the next phase
    phase_complete: Annotated[bool, last_value_wins]  # Flag to indicate if the current phase is complete
    org_level_complete: Annotated[bool, last_value_wins]  # Flag to indicate if organization-level research is complete
    session_id: Annotated[str, last_value_wins]  # Added for session tracking
    unit_number: Annotated[str, last_value_wins]  # Track which unit we're researching (for multi-unit plants)
    remaining_units: Annotated[list, last_value_wins]  # Units remaining to be processed
    current_unit: Annotated[str, last_value_wins]  # Current unit being processed
    all_units: Annotated[list, last_value_wins]  # All units in the plant
    unit_results: Annotated[list, accumulate_within_phase]  # Results for each unit processed
    extracted_units: NotRequired[Annotated[list, last_value_wins]]  # Units extracted from plant data for parallel processing
    no_units_found: NotRequired[Annotated[bool, last_value_wins]]  # Flag indicating if no units were found
    ready_for_parallel_processing: NotRequired[Annotated[bool, last_value_wins]]  # Flag indicating ready for parallel spawning
    
    # Smart Batching System - replaces matrix approach
    batch_processing_mode: NotRequired[Annotated[str, last_value_wins]]  # "smart_batching" or "serial"
    batch_size: NotRequired[Annotated[int, last_value_wins]]  # Optimal batch size for parallel processing
    batch_number: NotRequired[Annotated[int, last_value_wins]]  # Current batch being processed
    total_batches: NotRequired[Annotated[int, last_value_wins]]  # Total number of batches
    unit_priority: NotRequired[Annotated[dict, last_value_wins]]  # Priority scores for each unit
    
    # Unit Processing Results - collected from isolated states
    unit_processing_results: NotRequired[Annotated[list, accumulate_within_phase]]  # Results from all unit processing
    continue_batch_processing: NotRequired[Annotated[bool, last_value_wins]]  # Flag for batch continuation
    unit_batches: NotRequired[Annotated[list, last_value_wins]]  # Batch configuration
    unit_summary: NotRequired[Annotated[list, last_value_wins]]  # Summary of unit processing
    
    # Image Extraction Results - parallel processing
    image_extraction_complete: NotRequired[Annotated[bool, last_value_wins]]  # Flag for image extraction completion
    s3_image_urls: NotRequired[Annotated[list, last_value_wins]]  # S3 URLs of extracted images
    image_extraction_error: NotRequired[Annotated[str, last_value_wins]]  # Error message if image extraction failed
    
    # JSON S3 Storage - for storing structured data at each processing level
    s3_json_urls: NotRequired[Annotated[dict, last_value_wins]]  # S3 URLs for JSON files at each level
    json_storage_complete: NotRequired[Annotated[dict, last_value_wins]]  # Completion flags for each level
    json_storage_errors: NotRequired[Annotated[list, last_value_wins]]  # Storage error messages
    plant_name_for_s3: NotRequired[Annotated[str, last_value_wins]]  # Sanitized plant name for S3 folder
    
    # Plant Registry System - NEW for database-driven workflow
    plant_exists_in_db: NotRequired[Annotated[bool, last_value_wins]]  # Flag if plant found in registry
    existing_plant_info: NotRequired[Annotated[dict, last_value_wins]]  # Existing plant information from DB
    registry_check_complete: NotRequired[Annotated[bool, last_value_wins]]  # Registry check completion flag
    registry_error: NotRequired[Annotated[str, last_value_wins]]  # Registry check error message
    
    # Quick Organization Discovery - NEW for fast org extraction
    quick_discovery_complete: NotRequired[Annotated[bool, last_value_wins]]  # Quick discovery completion flag
    discovered_org_info: NotRequired[Annotated[dict, last_value_wins]]  # Organization info from quick discovery
    discovered_plants: NotRequired[Annotated[list, last_value_wins]]  # List of plants discovered for organization
    discovery_error: NotRequired[Annotated[str, last_value_wins]]  # Discovery error message
    
    # Organization UID System - NEW for parallel pipeline coordination
    org_uid: NotRequired[Annotated[str, last_value_wins]]  # Unique organization identifier
    org_name: NotRequired[Annotated[str, last_value_wins]]  # Organization name
    plant_country: NotRequired[Annotated[str, last_value_wins]]  # Plant country
    uid_generation_complete: NotRequired[Annotated[bool, last_value_wins]]  # UID generation completion flag
    uid_error: NotRequired[Annotated[str, last_value_wins]]  # UID generation error message
    
    # Financial Pipeline Integration - NEW for SQS-based parallel processing
    financial_pipeline_triggered: NotRequired[Annotated[bool, last_value_wins]]  # Financial pipeline trigger flag
    financial_trigger_error: NotRequired[Annotated[str, last_value_wins]]  # Financial trigger error message
    financial_trigger_message_id: NotRequired[Annotated[str, last_value_wins]]  # SQS message ID
    financial_trigger_sequence_number: NotRequired[Annotated[str, last_value_wins]]  # FIFO sequence number
    financial_trigger_timestamp: NotRequired[Annotated[str, last_value_wins]]  # Trigger timestamp
    financial_trigger_group_id: NotRequired[Annotated[str, last_value_wins]]  # FIFO message group ID
    
    # Database Population - NEW for registry maintenance
    database_population_complete: NotRequired[Annotated[bool, last_value_wins]]  # Database population completion flag
    plants_saved_count: NotRequired[Annotated[int, last_value_wins]]  # Number of plants saved to database
    database_error: NotRequired[Annotated[str, last_value_wins]]  # Database operation error message


# Isolated Unit State - completely separate from OverallState
class UnitState(TypedDict):
    """Isolated state for individual unit processing - prevents concurrent access conflicts"""
    
    # Core identification
    unit_number: str
    unit_session_id: str  # Unique session ID for this unit
    unit_batch_id: str   # Batch identifier
    
    # Unit-specific processing state
    messages: Annotated[list, add_messages]  # Shared read-only messages + unit-specific messages
    search_query: Annotated[list, accumulate_within_phase] 
    web_research_result: Annotated[list, accumulate_within_phase]
    sources_gathered: Annotated[list, accumulate_within_phase]
    
    # Unit processing control
    search_phase: Annotated[int, last_value_wins]  # Always 3 for unit-level
    research_loop_count: Annotated[int, last_value_wins]
    continue_research: Annotated[bool, last_value_wins]
    phase_complete: Annotated[bool, last_value_wins]
    
    # Unit-specific configuration
    initial_search_query_count: Annotated[int, last_value_wins]
    max_research_loops: Annotated[int, last_value_wins]
    reasoning_model: Annotated[str, last_value_wins]
    
    # Unit processing results
    unit_technical_data: Annotated[dict, last_value_wins]  # Final unit-level technical data
    processing_complete: Annotated[bool, last_value_wins]  # Flag indicating unit processing completion
    processing_error: Annotated[str, last_value_wins]     # Error message if processing failed
    
    # Context from plant-level (read-only)
    plant_name: str
    plant_country: str
    plant_technology: str


class ReflectionState(TypedDict):
    is_sufficient: bool
    knowledge_gap: str
    follow_up_queries: Annotated[list, accumulate_within_phase]
    research_loop_count: Annotated[int, last_value_wins]
    number_of_ran_queries: int
    search_phase: Annotated[int, last_value_wins]  # 1 for organization-level, 2 for plant-level, 3 for unit-level


class Query(TypedDict):
    query: str
    rationale: str


class QueryGenerationState(TypedDict):
    query_list: list[Query]
    search_phase: Annotated[int, last_value_wins]  # 1 for organization-level, 2 for plant-level


class WebSearchState(TypedDict):
    search_query: str
    id: int  # Changed from str to int for consistency
    search_phase: Annotated[int, last_value_wins]  # 1 for organization-level, 2 for plant-level
    unit_number: Annotated[str, last_value_wins]  # Track which unit we're researching (for unit-level searches)


@dataclass
class SearchStateOutput:
    running_summary: str = field(default=None)  # Final report
