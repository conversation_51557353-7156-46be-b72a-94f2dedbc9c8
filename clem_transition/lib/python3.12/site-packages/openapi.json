{"openapi": "3.1.0", "info": {"title": "LangGraph Platform", "version": "0.1.0"}, "tags": [{"name": "Assistants", "description": "An assistant is a configured instance of a graph."}, {"name": "Threads", "description": "A thread contains the accumulated outputs of a group of runs."}, {"name": "Thread Runs", "description": "A run is an invocation of a graph / assistant on a thread. It updates the state of the thread."}, {"name": "Stateless Runs", "description": "A run is an invocation of a graph / assistant, with no state or memory persistence."}, {"name": "Crons (Plus tier)", "description": "A cron is a periodic run that recurs on a given schedule. The repeats can be isolated, or share state in a thread"}, {"name": "Store", "description": "Store is an API for managing persistent key-value store (long-term memory) that is available from any thread."}], "paths": {"/assistants": {"post": {"tags": ["Assistants"], "summary": "Create Assistant", "description": "Create an assistant.\n\nAn initial version of the assistant will be created and the assistant is set to that version. To change versions, use the `POST /assistants/{assistant_id}/latest` endpoint.", "operationId": "create_assistant_assistants_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssistantCreate"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Assistant"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/assistants/search": {"post": {"tags": ["Assistants"], "summary": "Search Assistants", "description": "Search for assistants.\n\nThis endpoint also functions as the endpoint to list all assistants.", "operationId": "search_assistants_assistants_search_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssistantSearchRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/Assistant"}, "type": "array", "title": "Response Search Assistants Assistants Search Post"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/assistants/{assistant_id}": {"get": {"tags": ["Assistants"], "summary": "Get Assistant", "description": "Get an assistant by ID.", "operationId": "get_assistant_assistants__assistant_id__get", "parameters": [{"description": "The ID of the assistant.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Assistant ID", "description": "The ID of the assistant."}, "name": "assistant_id", "in": "path"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Assistant"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["Assistants"], "summary": "Delete Assistant", "description": "Delete an assistant by <PERSON>.\n\nAll versions of the assistant will be deleted as well.", "operationId": "delete_assistant_assistants__assistant_id__delete", "parameters": [{"description": "The ID of the assistant.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Assistant ID", "description": "The ID of the assistant."}, "name": "assistant_id", "in": "path"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "patch": {"tags": ["Assistants"], "summary": "<PERSON> Assistant", "description": "Update an assistant.", "operationId": "patch_assistant_assistants__assistant_id__patch", "parameters": [{"description": "The ID of the assistant.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Assistant ID", "description": "The ID of the assistant."}, "name": "assistant_id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssistantPatch"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Assistant"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/assistants/{assistant_id}/graph": {"get": {"tags": ["Assistants"], "summary": "Get Assistant Graph", "description": "Get an assistant by ID.", "operationId": "get_assistant_graph_assistants__assistant_id__graph_get", "parameters": [{"description": "The ID of the assistant.", "required": true, "schema": {"anyOf": [{"type": "string", "format": "uuid", "title": "Assistant ID", "description": "The ID of the assistant."}, {"type": "string", "title": "Graph ID", "description": "The ID of the graph."}]}, "name": "assistant_id", "in": "path"}, {"description": "Include graph representation of subgraphs. If an integer value is provided, only subgraphs with a depth less than or equal to the value will be included.", "required": false, "schema": {"oneOf": [{"type": "boolean"}, {"type": "integer"}], "title": "Xray", "default": false, "description": "Include graph representation of subgraphs. If an integer value is provided, only subgraphs with a depth less than or equal to the value will be included."}, "name": "xray", "in": "query"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"additionalProperties": {"items": {"type": "object"}, "type": "array"}, "type": "object", "title": "Response Get Assistant Graph Assistants  Assistant <PERSON><PERSON><PERSON>"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/assistants/{assistant_id}/subgraphs": {"get": {"tags": ["Assistants"], "summary": "Get Assistant Subgraphs", "description": "Get an assistant's subgraphs.", "operationId": "get_assistant_subgraphs_assistants__assistant_id__subgraphs_get", "parameters": [{"description": "The ID of the assistant.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Assistant Id"}, "name": "assistant_id", "in": "path"}, {"description": "Recursively retrieve subgraphs of subgraphs.", "required": false, "schema": {"type": "boolean", "title": "Recurse", "default": false}, "name": "recurse", "in": "query"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Subgraphs"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/assistants/{assistant_id}/subgraphs/{namespace}": {"get": {"tags": ["Assistants"], "summary": "Get Assistant Subgraphs by Namespace", "description": "Get an assistant's subgraphs filtered by namespace.", "operationId": "get_assistant_subgraphs_assistants__assistant_id__subgraphs__namespace__get", "parameters": [{"description": "The ID of the assistant.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Assistant Id"}, "name": "assistant_id", "in": "path"}, {"description": "Namespace of the subgraph to filter by.", "required": true, "schema": {"type": "string", "title": "Namespace"}, "name": "namespace", "in": "path"}, {"description": "Recursively retrieve subgraphs of subgraphs.", "required": false, "schema": {"type": "boolean", "title": "Recurse", "default": false}, "name": "recurse", "in": "query"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Subgraphs"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/assistants/{assistant_id}/schemas": {"get": {"tags": ["Assistants"], "summary": "Get <PERSON>", "description": "Get an assistant by ID.", "operationId": "get_assistant_schemas_assistants__assistant_id__schemas_get", "parameters": [{"description": "The ID of the assistant.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Assistant Id", "description": "The ID of the assistant."}, "name": "assistant_id", "in": "path"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GraphSchema"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/assistants/{assistant_id}/versions": {"post": {"tags": ["Assistants"], "summary": "Get Assistant Versions", "description": "Get all versions of an assistant.", "operationId": "get_assistant_versions_assistants__assistant_id__versions_get", "parameters": [{"description": "The ID of the assistant.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Assistant Id", "description": "The ID of the assistant."}, "name": "assistant_id", "in": "path"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/Assistant"}, "type": "array", "title": "Response Search Assistants Assistants Search Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/assistants/{assistant_id}/latest": {"post": {"tags": ["Assistants"], "summary": "Set Latest Assistant Version", "description": "Set the latest version for an assistant.", "operationId": "set_latest_assistant_version_assistants__assistant_id__versions_post", "parameters": [{"description": "The ID of the assistant.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Assistant Id", "description": "The ID of the assistant."}, "name": "assistant_id", "in": "path"}, {"description": "The version to change to.", "required": true, "schema": {"type": "integer", "title": "Version", "description": "The version of the assistant to change to."}, "name": "version", "in": "query"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Assistant"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/threads": {"post": {"tags": ["Threads"], "summary": "Create Thread", "description": "Create a thread.", "operationId": "create_thread_threads_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThreadCreate"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Thread"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/threads/search": {"post": {"tags": ["Threads"], "summary": "Search Threads", "description": "Search for threads.\n\nThis endpoint also functions as the endpoint to list all threads.", "operationId": "search_threads_threads_search_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThreadSearchRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/Thread"}, "type": "array", "title": "Response Search Threads Threads Search Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/threads/{thread_id}/state": {"get": {"tags": ["Threads"], "summary": "Get Thread State", "description": "Get state for a thread.\n\nThe latest state of the thread (i.e. latest checkpoint) is returned.", "operationId": "get_latest_thread_state_threads__thread_id__state_get", "parameters": [{"description": "The ID of the thread.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "name": "thread_id", "in": "path"}, {"description": "Whether to include subgraphs in the response.", "required": false, "schema": {"type": "boolean", "title": "Subgraphs", "description": "Whether to include subgraphs in the response."}, "name": "subgraphs", "in": "query"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThreadState"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["Threads"], "summary": "Update Thread State", "description": "Add state to a thread.", "operationId": "update_thread_state_threads__thread_id__state_post", "parameters": [{"description": "The ID of the thread.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "name": "thread_id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThreadStateUpdate"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThreadStateUpdateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/threads/{thread_id}/state/{checkpoint_id}": {"get": {"tags": ["Threads"], "summary": "Get Thread State At Checkpoint", "description": "Get state for a thread at a specific checkpoint.", "operationId": "get_thread_state_at_checkpoint_threads__thread_id__state__checkpoint_id__get", "parameters": [{"description": "The ID of the thread.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "name": "thread_id", "in": "path"}, {"description": "The ID of the checkpoint.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Checkpoint Id", "description": "The ID of the checkpoint."}, "name": "checkpoint_id", "in": "path"}, {"description": "Whether to include subgraphs in the response.", "required": false, "schema": {"type": "boolean", "title": "Subgraphs", "description": "Whether to include subgraphs in the response."}, "name": "subgraphs", "in": "query"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThreadState"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/threads/{thread_id}/state/checkpoint": {"post": {"tags": ["Threads"], "summary": "Get Thread State At Checkpoint", "description": "Get state for a thread at a specific checkpoint.", "operationId": "post_thread_state_at_checkpoint_threads__thread_id__state__checkpoint_id__get", "parameters": [{"description": "The ID of the thread.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "name": "thread_id", "in": "path"}, {"required": false, "schema": {"type": "boolean", "title": "Subgraphs", "description": "If true, includes subgraph states."}, "name": "subgraphs", "in": "query"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThreadStateCheckpointRequest"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThreadState"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/threads/{thread_id}/history": {"get": {"tags": ["Threads"], "summary": "Get Thread History", "description": "Get all past states for a thread.", "operationId": "get_thread_history_threads__thread_id__history_get", "parameters": [{"description": "The ID of the thread.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "name": "thread_id", "in": "path"}, {"required": false, "schema": {"type": "integer", "title": "Limit", "default": 10}, "name": "limit", "in": "query"}, {"required": false, "schema": {"type": "string", "title": "Before"}, "name": "before", "in": "query"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ThreadState"}, "type": "array", "title": "Response Get Thread History Threads  Thread Id  History Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["Threads"], "summary": "Get Thread History Post", "description": "Get all past states for a thread.", "operationId": "get_thread_history_post_threads__thread_id__history_post", "parameters": [{"description": "The ID of the thread.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "name": "thread_id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThreadStateSearch"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ThreadState"}, "type": "array", "title": "Response Get Thread History Post Threads  Thread Id  History Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/threads/{thread_id}/copy": {"post": {"tags": ["Threads"], "summary": "<PERSON><PERSON>ead", "description": "Create a new thread with a copy of the state and checkpoints from an existing thread.", "operationId": "copy_thread_post_threads__thread_id__copy_post", "parameters": [{"description": "The ID of the thread.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "name": "thread_id", "in": "path"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Thread"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/threads/{thread_id}": {"get": {"tags": ["Threads"], "summary": "Get Thread", "description": "Get a thread by ID.", "operationId": "get_thread_threads__thread_id__get", "parameters": [{"description": "The ID of the thread.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "name": "thread_id", "in": "path"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Thread"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["Threads"], "summary": "Delete Thread", "description": "Delete a thread by ID.", "operationId": "delete_thread_threads__thread_id__delete", "parameters": [{"description": "The ID of the thread.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "name": "thread_id", "in": "path"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "patch": {"tags": ["Threads"], "summary": "<PERSON>", "description": "Update a thread.", "operationId": "patch_thread_threads__thread_id__patch", "parameters": [{"description": "The ID of the thread.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "name": "thread_id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThreadPatch"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Thread"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/threads/{thread_id}/runs": {"get": {"tags": ["Thread Runs"], "summary": "List Runs", "description": "List runs for a thread.", "operationId": "list_runs_http_threads__thread_id__runs_get", "parameters": [{"description": "The ID of the thread.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "name": "thread_id", "in": "path"}, {"required": false, "schema": {"type": "integer", "title": "Limit", "default": 10}, "name": "limit", "in": "query"}, {"required": false, "schema": {"type": "integer", "title": "Offset", "default": 0}, "name": "offset", "in": "query"}, {"required": false, "schema": {"type": "string", "enum": ["pending", "error", "success", "timeout", "interrupted"]}, "name": "status", "in": "query"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/Run"}, "type": "array"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["Thread Runs"], "summary": "Create Background Run", "description": "Create a run in existing thread, return the run ID immediately. Don't wait for the final run output.", "operationId": "create_run_threads__thread_id__runs_post", "parameters": [{"description": "The ID of the thread.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "name": "thread_id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RunCreateStateful"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Run"}}}, "headers": {"Content-Location": {"description": "The URL of the run that was created. Can be used to later join the stream.", "schema": {"type": "string"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/threads/{thread_id}/runs/crons": {"post": {"tags": ["Crons (Plus tier)"], "summary": "Create <PERSON><PERSON><PERSON>", "description": "Create a cron to schedule runs on a thread.", "operationId": "create_thread_cron_threads__thread_id__runs_crons_post", "parameters": [{"description": "The ID of the thread.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "name": "thread_id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CronCreate"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cron"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/threads/{thread_id}/runs/stream": {"post": {"tags": ["Thread Runs"], "summary": "Create Run, Stream Output", "description": "Create a run in existing thread. Stream the output.", "operationId": "stream_run_threads__thread_id__runs_stream_post", "parameters": [{"description": "The ID of the thread.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "name": "thread_id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RunCreateStateful"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"text/event-stream": {"schema": {"type": "string", "description": "The server will send a stream of events in SSE format.\n\n**Example event**:\n\nid: 1\n\nevent: message\n\ndata: {}"}}}, "headers": {"Content-Location": {"description": "The URL of the run that was created. Can be used to later join the stream.", "schema": {"type": "string"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/threads/{thread_id}/runs/wait": {"post": {"tags": ["Thread Runs"], "summary": "Create Run, Wait for Output", "description": "Create a run in existing thread. Wait for the final output and then return it.", "operationId": "wait_run_threads__thread_id__runs_wait_post", "parameters": [{"description": "The ID of the thread.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "name": "thread_id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RunCreateStateful"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {}}}, "headers": {"Content-Location": {"description": "The URL of the run that was created. Can be used to later join the stream.", "schema": {"type": "string"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/threads/{thread_id}/runs/{run_id}": {"get": {"tags": ["Thread Runs"], "summary": "Get Run", "description": "Get a run by <PERSON>.", "operationId": "get_run_http_threads__thread_id__runs__run_id__get", "parameters": [{"description": "The ID of the thread.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "name": "thread_id", "in": "path"}, {"description": "The ID of the run.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Run Id", "description": "The ID of the run."}, "name": "run_id", "in": "path"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Run"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["Thread Runs"], "summary": "Delete Run", "description": "Delete a run by <PERSON>.", "operationId": "delete_run_threads__thread_id__runs__run_id__delete", "parameters": [{"description": "The ID of the thread.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "name": "thread_id", "in": "path"}, {"description": "The ID of the run.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Run Id", "description": "The ID of the run."}, "name": "run_id", "in": "path"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/threads/{thread_id}/runs/{run_id}/join": {"get": {"tags": ["Thread Runs"], "summary": "Join <PERSON>", "description": "Wait for a run to finish.", "operationId": "join_run_http_threads__thread_id__runs__run_id__join_get", "parameters": [{"description": "The ID of the thread.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "name": "thread_id", "in": "path"}, {"description": "The ID of the run.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Run Id", "description": "The ID of the run."}, "name": "run_id", "in": "path"}, {"required": false, "schema": {"type": "boolean", "title": "Cancel on Disconnect", "description": "If true, the run will be cancelled if the client disconnects.", "default": false}, "name": "cancel_on_disconnect", "in": "query"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/threads/{thread_id}/runs/{run_id}/stream": {"get": {"tags": ["Thread Runs"], "summary": "Join <PERSON>", "description": "Join a run stream. This endpoint streams output in real-time from a run similar to the /threads/__THREAD_ID__/runs/stream endpoint. If the run has been created with `stream_resumable=true`, the stream can be resumed from the last seen event ID.", "operationId": "stream_run_http_threads__thread_id__runs__run_id__join_get", "parameters": [{"description": "The ID of the thread.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "name": "thread_id", "in": "path"}, {"description": "The ID of the run.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Run Id", "description": "The ID of the run."}, "name": "run_id", "in": "path"}, {"required": false, "schema": {"type": "string", "title": "Last Event ID", "description": "The ID of the last event received. Set to -1 if you want to stream all events. Requires `stream_resumable=true` to be set when creating the run."}, "name": "Last-Event-ID", "in": "header"}, {"required": false, "schema": {"type": "string", "title": "Stream Mode", "description": "The mode to stream the run in. If not provided, the default mode will be used."}, "name": "stream_mode", "in": "query"}, {"required": false, "schema": {"type": "boolean", "title": "Cancel On Disconnect", "description": "If true, the run will be cancelled if the client disconnects.", "default": false}, "name": "cancel_on_disconnect", "in": "query"}], "responses": {"200": {"description": "Success", "content": {"text/event-stream": {"schema": {"type": "string", "description": "The server will send a stream of events in SSE format.\n\n**Example event**:\n\nid: 1\n\nevent: message\n\ndata: {}"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/threads/{thread_id}/runs/{run_id}/cancel": {"post": {"tags": ["Thread Runs"], "summary": "Cancel Run", "operationId": "cancel_run_http_threads__thread_id__runs__run_id__cancel_post", "parameters": [{"description": "The ID of the thread.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "name": "thread_id", "in": "path"}, {"description": "The ID of the run.", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Run Id", "description": "The ID of the run."}, "name": "run_id", "in": "path"}, {"required": false, "schema": {"type": "boolean", "title": "Wait", "default": false}, "name": "wait", "in": "query"}, {"description": "Action to take when cancelling the run. Possible values are `interrupt` or `rollback`. `interrupt` will simply cancel the run. `rollback` will cancel the run and delete the run and associated checkpoints afterwards.", "required": false, "schema": {"type": "string", "enum": ["interrupt", "rollback"], "title": "Action", "default": "interrupt"}, "name": "action", "in": "query"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/runs/crons": {"post": {"tags": ["Crons (Plus tier)"], "summary": "C<PERSON> <PERSON><PERSON>", "description": "Create a cron to schedule runs on new threads.", "operationId": "create_cron_runs_crons_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CronCreate"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cron"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/runs/crons/search": {"post": {"tags": ["Crons (Plus tier)"], "summary": "Search Crons", "description": "Search all active crons", "operationId": "search_crons_runs_crons_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CronSearch"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/Cron"}, "type": "array", "title": "Response Search Crons Search Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/runs/stream": {"post": {"tags": ["Stateless Runs"], "summary": "Create Run, Stream Output", "description": "Create a run in a new thread, stream the output.", "operationId": "stream_run_stateless_runs_stream_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RunCreateStateless"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"text/event-stream": {"schema": {"type": "string", "description": "The server will send a stream of events in SSE format.\n\n**Example event**:\n\nid: 1\n\nevent: message\n\ndata: {}"}}}, "headers": {"Content-Location": {"description": "The URL of the run that was created. Can be used to later join the stream.", "schema": {"type": "string"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/runs/cancel": {"post": {"tags": ["Thread Runs"], "summary": "Cancel Runs", "description": "Cancel one or more runs. Can cancel runs by thread ID and run IDs, or by status filter.", "operationId": "cancel_runs_post", "parameters": [{"description": "Action to take when cancelling the run. Possible values are `interrupt` or `rollback`. `interrupt` will simply cancel the run. `rollback` will cancel the run and delete the run and associated checkpoints afterwards.", "required": false, "schema": {"type": "string", "enum": ["interrupt", "rollback"], "title": "Action", "default": "interrupt"}, "name": "action", "in": "query"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RunsCancel"}}}, "required": true}, "responses": {"204": {"description": "Success - Runs cancelled"}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/runs/wait": {"post": {"tags": ["Stateless Runs"], "summary": "Create Run, Wait for Output", "description": "Create a run in a new thread. Wait for the final output and then return it.", "operationId": "wait_run_stateless_runs_wait_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RunCreateStateless"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {}}}, "headers": {"Content-Location": {"description": "The URL of the run that was created. Can be used to later join the stream.", "schema": {"type": "string"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/runs": {"post": {"tags": ["Stateless Runs"], "summary": "Create Background Run", "description": "Create a run in a new thread, return the run ID immediately. Don't wait for the final run output.", "operationId": "run_stateless_runs_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RunCreateStateless"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {}}}, "headers": {"Content-Location": {"description": "The URL of the run that was created. Can be used to later join the stream.", "schema": {"type": "string"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/runs/batch": {"post": {"tags": ["Stateless Runs"], "summary": "Create <PERSON>", "description": "Create a batch of runs in new threads, return immediately.", "operationId": "run_batch_stateless_runs_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RunBatchCreate"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/runs/crons/{cron_id}": {"delete": {"tags": ["Crons (Plus tier)"], "summary": "Delete Cron", "description": "Delete a cron by ID.", "operationId": "delete_cron_runs_crons__cron_id__delete", "parameters": [{"required": true, "schema": {"type": "string", "format": "uuid", "title": "Cron Id"}, "name": "cron_id", "in": "path"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/store/items": {"put": {"tags": ["Store"], "summary": "Store or update an item.", "operationId": "put_item", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StorePutRequest"}}}}, "responses": {"204": {"description": "Success"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["Store"], "summary": "Delete an item.", "operationId": "delete_item", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreDeleteRequest"}}}}, "responses": {"204": {"description": "Success"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "get": {"tags": ["Store"], "summary": "Retrieve a single item.", "operationId": "get_item", "parameters": [{"name": "key", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "namespace", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Item"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/store/items/search": {"post": {"tags": ["Store"], "summary": "Search for items within a namespace prefix.", "operationId": "search_items", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreSearchRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchItemsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/store/namespaces": {"post": {"tags": ["Store"], "summary": "List namespaces with optional match conditions.", "operationId": "list_namespaces", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreListNamespacesRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListNamespaceResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/mcp/": {"post": {"operationId": "post_mcp", "summary": "MCP Post", "description": "Implemented according to the Streamable HTTP Transport specification.\nSends a JSON-RPC 2.0 message to the server.\n\n- **Request**: Provide an object with `jsonrpc`, `id`, `method`, and optional `params`.\n- **Response**: Returns a JSON-RPC response or acknowledgment.\n\n**Notes:**\n- Stateless: Sessions are not persisted across requests.\n", "parameters": [{"name": "Accept", "in": "header", "required": true, "schema": {"type": "string", "enum": ["application/json, text/event-stream"]}, "description": "Accept header must include both 'application/json' and 'text/event-stream' media types."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}, "description": "A JSON-RPC 2.0 request, notification, or response object.", "example": {"jsonrpc": "2.0", "id": "1", "method": "initialize", "params": {"clientInfo": {"name": "test_client", "version": "1.0.0"}, "protocolVersion": "2024-11-05", "capabilities": {}}}}}}, "responses": {"200": {"description": "Successful JSON-RPC response.", "content": {"application/json": {"schema": {"type": "object"}}}}, "202": {"description": "Notification or response accepted; no content body."}, "400": {"description": "Bad request: invalid JSON or message format, or unacceptable Accept header."}, "405": {"description": "HTTP method not allowed."}, "500": {"description": "Internal server error or unexpected failure."}}, "tags": ["MCP"]}, "get": {"operationId": "get_mcp", "summary": "MCP Get", "description": "Implemented according to the Streamable HTTP Transport specification.", "responses": {"405": {"description": "GET method not allowed; streaming not supported."}}, "tags": ["MCP"]}, "delete": {"operationId": "delete_mcp", "summary": "Terminate Session", "description": "Implemented according to the Streamable HTTP Transport specification.\nTerminate an MCP session. The server implementation is stateless, so this is a no-op.\n\n", "responses": {"404": {}}, "tags": ["MCP"]}}}, "components": {"schemas": {"Assistant": {"properties": {"assistant_id": {"type": "string", "format": "uuid", "title": "Assistant Id", "description": "The ID of the assistant."}, "graph_id": {"type": "string", "title": "Graph Id", "description": "The ID of the graph."}, "config": {"properties": {"tags": {"items": {"type": "string"}, "type": "array", "title": "Tags"}, "recursion_limit": {"type": "integer", "title": "Recursion Limit"}, "configurable": {"type": "object", "title": "Configurable"}}, "type": "object", "title": "Config", "description": "The assistant config."}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "The time the assistant was created."}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "The last time the assistant was updated."}, "metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>", "description": "The assistant metadata."}, "version": {"type": "integer", "title": "Version", "description": "The version of the assistant"}, "name": {"type": "string", "title": "Assistant Name", "description": "The name of the assistant"}, "description": {"type": ["string", "null"], "title": "Assistant Description", "description": "The description of the assistant"}}, "type": "object", "required": ["assistant_id", "graph_id", "config", "created_at", "updated_at", "metadata"], "title": "Assistant"}, "AssistantCreate": {"properties": {"assistant_id": {"type": "string", "format": "uuid", "title": "Assistant Id", "description": "The ID of the assistant. If not provided, a random UUID will be generated."}, "graph_id": {"type": "string", "title": "Graph Id", "description": "The ID of the graph the assistant should use. The graph ID is normally set in your langgraph.json configuration."}, "config": {"type": "object", "title": "Config", "description": "Configuration to use for the graph. Useful when graph is configurable and you want to create different assistants based on different configurations."}, "metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> to add to assistant."}, "if_exists": {"type": "string", "enum": ["raise", "do_nothing"], "title": "If Exists", "description": "How to handle duplicate creation. Must be either 'raise' (raise error if duplicate), or 'do_nothing' (return existing assistant).", "default": "raise"}, "name": {"type": "string", "title": "Name", "description": "The name of the assistant. Defaults to 'Untitled'."}, "description": {"type": ["string", "null"], "title": "Description", "description": "The description of the assistant. Defaults to null."}}, "type": "object", "required": ["graph_id"], "title": "AssistantCreate", "description": "Payload for creating an assistant."}, "AssistantPatch": {"properties": {"graph_id": {"type": "string", "title": "Graph Id", "description": "The ID of the graph the assistant should use. The graph ID is normally set in your langgraph.json configuration. If not provided, assistant will keep pointing to same graph."}, "config": {"type": "object", "title": "Config", "description": "Configuration to use for the graph. Useful when graph is configurable and you want to update the assistant's configuration."}, "metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>", "description": "Metadata to merge with existing assistant metadata."}, "name": {"type": "string", "title": "Name", "description": "The new name for the assistant. If not provided, assistant will keep its current name."}, "description": {"type": "string", "title": "Description", "description": "The new description for the assistant. If not provided, assistant will keep its current description."}}, "type": "object", "title": "Assistant<PERSON><PERSON>", "description": "Payload for updating an assistant."}, "AssistantVersionChange": {"properties": {"version": {"type": "integer", "title": "Version", "description": "The assistant version."}}, "type": "object", "title": "Assistant<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Payload for changing the version of an assistant."}, "Config": {"properties": {"tags": {"items": {"type": "string"}, "type": "array", "title": "Tags"}, "recursion_limit": {"type": "integer", "title": "Recursion Limit"}, "configurable": {"type": "object", "title": "Configurable"}}, "type": "object", "title": "Config"}, "Cron": {"properties": {"cron_id": {"type": "string", "format": "uuid", "title": "Cron Id", "description": "The ID of the cron."}, "thread_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "end_time": {"type": "string", "format": "date-time", "title": "End Time", "description": "The end date to stop running the cron."}, "schedule": {"type": "string", "title": "Schedule", "description": "The schedule to run, cron format."}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "The time the cron was created."}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "The last time the cron was updated."}, "payload": {"type": "object", "title": "Payload", "description": "The run payload to use for creating new run."}}, "type": "object", "required": ["cron_id", "thread_id", "end_time", "schedule", "created_at", "updated_at", "payload"], "title": "<PERSON><PERSON>", "description": "Represents a scheduled task."}, "CronCreate": {"properties": {"schedule": {"type": "string", "title": "Schedule", "description": "The cron schedule to execute this job on."}, "end_time": {"type": "string", "format": "date-time", "title": "End Time", "description": "The end date to stop running the cron."}, "assistant_id": {"anyOf": [{"type": "string", "format": "uuid", "title": "Assistant Id"}, {"type": "string", "title": "Graph Id"}], "description": "The assistant ID or graph name to run. If using graph name, will default to the assistant automatically created from that graph by the server."}, "input": {"anyOf": [{"items": {"type": "object"}, "type": "array"}, {"type": "object"}], "title": "Input", "description": "The input to the graph."}, "metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> to assign to the cron job runs."}, "config": {"properties": {"tags": {"items": {"type": "string"}, "type": "array", "title": "Tags"}, "recursion_limit": {"type": "integer", "title": "Recursion Limit"}, "configurable": {"type": "object", "title": "Configurable"}}, "type": "object", "title": "Config", "description": "The configuration for the assistant."}, "webhook": {"type": "string", "maxLength": 65536, "minLength": 1, "format": "uri-reference", "title": "Webhook", "description": "Webhook to call after LangGraph API call is done."}, "interrupt_before": {"anyOf": [{"type": "string", "enum": ["*"]}, {"items": {"type": "string"}, "type": "array"}], "title": "Interrupt Before", "description": "Nodes to interrupt immediately before they get executed."}, "interrupt_after": {"anyOf": [{"type": "string", "enum": ["*"]}, {"items": {"type": "string"}, "type": "array"}], "title": "Interrupt After", "description": "Nodes to interrupt immediately after they get executed."}, "multitask_strategy": {"type": "string", "enum": ["reject", "rollback", "interrupt", "enqueue"], "title": "Multitask Strategy", "description": "Multitask strategy to use. Must be one of 'reject', 'interrupt', 'rollback', or 'enqueue'.", "default": "reject"}}, "type": "object", "required": ["assistant_id", "schedule"], "title": "CronCreate", "description": "Payload for creating a cron job."}, "CronSearch": {"properties": {"assistant_id": {"type": "string", "format": "uuid", "title": "Assistant Id", "description": "The assistant ID or graph name to search for."}, "thread_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The thread ID to search for."}, "limit": {"type": "integer", "title": "Limit", "description": "The maximum number of results to return.", "default": 10, "minimum": 1, "maximum": 1000}, "offset": {"type": "integer", "title": "Offset", "description": "The number of results to skip.", "default": 0, "minimum": 0}, "sort_by": {"type": "string", "title": "Sort By", "description": "The field to sort by.", "default": "created_at", "enum": ["cron_id", "assistant_id", "thread_id", "next_run_date", "end_time", "created_at", "updated_at"]}, "sort_order": {"type": "string", "title": "Sort Order", "description": "The order to sort by.", "default": "desc", "enum": ["asc", "desc"]}}, "type": "object", "required": [], "title": "CronSearch", "description": "Payload for listing crons"}, "GraphSchema": {"properties": {"graph_id": {"type": "string", "title": "Graph Id", "description": "The ID of the graph."}, "input_schema": {"type": "object", "title": "Input Schema", "description": "The schema for the graph input. Missing if unable to generate JSON schema from graph."}, "output_schema": {"type": "object", "title": "Output Schema", "description": "The schema for the graph output. Missing if unable to generate JSON schema from graph."}, "state_schema": {"type": "object", "title": "State Schema", "description": "The schema for the graph state. Missing if unable to generate JSON schema from graph."}, "config_schema": {"type": "object", "title": "Config <PERSON>", "description": "The schema for the graph config. Missing if unable to generate JSON schema from graph."}}, "type": "object", "required": ["graph_id", "state_schema", "config_schema"], "title": "GraphSchema", "description": "Defines the structure and properties of a graph."}, "GraphSchemaNoId": {"properties": {"input_schema": {"type": "object", "title": "Input Schema", "description": "The schema for the graph input. Missing if unable to generate JSON schema from graph."}, "output_schema": {"type": "object", "title": "Output Schema", "description": "The schema for the graph output. Missing if unable to generate JSON schema from graph."}, "state_schema": {"type": "object", "title": "State Schema", "description": "The schema for the graph state. Missing if unable to generate JSON schema from graph."}, "config_schema": {"type": "object", "title": "Config <PERSON>", "description": "The schema for the graph config. Missing if unable to generate JSON schema from graph."}}, "type": "object", "required": ["input_schema", "output_schema", "state_schema", "config_schema"], "title": "GraphSchemaNoId", "description": "Defines the structure and properties of a graph without an ID."}, "Subgraphs": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/GraphSchemaNoId"}, "title": "Subgraphs", "description": "Map of graph name to graph schema metadata (`input_schema`, `output_schema`, `state_schema`, `config_schema`)."}, "Run": {"properties": {"run_id": {"type": "string", "format": "uuid", "title": "Run Id", "description": "The ID of the run."}, "thread_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "assistant_id": {"type": "string", "format": "uuid", "title": "Assistant Id", "description": "The assistant that was used for this run."}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "The time the run was created."}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "The last time the run was updated."}, "status": {"type": "string", "enum": ["pending", "running", "error", "success", "timeout", "interrupted"], "title": "Status", "description": "The status of the run. One of 'pending', 'running', 'error', 'success', 'timeout', 'interrupted'."}, "metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>", "description": "The run metadata."}, "kwargs": {"type": "object", "title": "Kwar<PERSON>"}, "multitask_strategy": {"type": "string", "enum": ["reject", "rollback", "interrupt", "enqueue"], "title": "Multitask Strategy", "description": "Strategy to handle concurrent runs on the same thread."}}, "type": "object", "required": ["run_id", "thread_id", "assistant_id", "created_at", "updated_at", "status", "metadata", "kwargs", "multitask_strategy"], "title": "Run"}, "Send": {"type": "object", "title": "Send", "description": "A message to send to a node.", "properties": {"node": {"type": "string", "title": "Node", "description": "The node to send the message to."}, "input": {"type": ["object", "array", "number", "string", "boolean", "null"], "title": "Message", "description": "The message to send."}}, "required": ["node", "input"]}, "Command": {"type": "object", "title": "Command", "description": "The command to run.", "properties": {"update": {"type": ["object", "array", "null"], "title": "Update", "description": "An update to the state."}, "resume": {"type": ["object", "array", "number", "string", "boolean", "null"], "title": "Resume", "description": "A value to pass to an interrupted node."}, "goto": {"anyOf": [{"$ref": "#/components/schemas/Send"}, {"type": "array", "items": {"$ref": "#/components/schemas/Send"}}, {"type": "string"}, {"type": "array", "items": {"type": "string"}}, {"type": "null"}], "title": "<PERSON><PERSON>", "description": "Name of the node(s) to navigate to next or node(s) to be executed with a provided input."}}}, "RunCreateStateful": {"properties": {"assistant_id": {"anyOf": [{"type": "string", "format": "uuid", "title": "Assistant Id"}, {"type": "string", "title": "Graph Id"}], "description": "The assistant ID or graph name to run. If using graph name, will default to first assistant created from that graph."}, "checkpoint": {"type": "object", "title": "Checkpoint", "description": "The checkpoint to resume from.", "$ref": "#/components/schemas/CheckpointConfig"}, "input": {"anyOf": [{"type": "object"}, {"type": "array"}, {"type": "string"}, {"type": "number"}, {"type": "boolean"}, {"type": "null"}], "title": "Input", "description": "The input to the graph."}, "command": {"anyOf": [{"$ref": "#/components/schemas/Command"}, {"type": "null"}], "title": "Input", "description": "The input to the graph."}, "metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> to assign to the run."}, "config": {"properties": {"tags": {"items": {"type": "string"}, "type": "array", "title": "Tags"}, "recursion_limit": {"type": "integer", "title": "Recursion Limit"}, "configurable": {"type": "object", "title": "Configurable"}}, "type": "object", "title": "Config", "description": "The configuration for the assistant."}, "webhook": {"type": "string", "maxLength": 65536, "minLength": 1, "format": "uri-reference", "title": "Webhook", "description": "Webhook to call after LangGraph API call is done."}, "interrupt_before": {"anyOf": [{"type": "string", "enum": ["*"]}, {"items": {"type": "string"}, "type": "array"}], "title": "Interrupt Before", "description": "Nodes to interrupt immediately before they get executed."}, "interrupt_after": {"anyOf": [{"type": "string", "enum": ["*"]}, {"items": {"type": "string"}, "type": "array"}], "title": "Interrupt After", "description": "Nodes to interrupt immediately after they get executed."}, "stream_mode": {"anyOf": [{"items": {"type": "string", "enum": ["values", "messages", "messages-tuple", "updates", "events", "debug", "custom"]}, "type": "array"}, {"type": "string", "enum": ["values", "messages", "messages-tuple", "updates", "events", "debug", "custom"]}], "title": "Stream Mode", "description": "The stream mode(s) to use.", "default": ["values"]}, "stream_subgraphs": {"type": "boolean", "title": "Stream Subgraphs", "description": "Whether to stream output from subgraphs.", "default": false}, "stream_resumable": {"type": "boolean", "title": "Stream Resumable", "description": "Whether to persist the stream chunks in order to resume the stream later.", "default": false}, "on_disconnect": {"type": "string", "enum": ["cancel", "continue"], "title": "On Disconnect", "description": "The disconnect mode to use. Must be one of 'cancel' or 'continue'.", "default": "cancel"}, "feedback_keys": {"items": {"type": "string"}, "type": "array", "title": "<PERSON><PERSON><PERSON>", "description": "Feedback keys to assign to run."}, "multitask_strategy": {"type": "string", "enum": ["reject", "rollback", "interrupt", "enqueue"], "title": "Multitask Strategy", "description": "Multitask strategy to use. Must be one of 'reject', 'interrupt', 'rollback', or 'enqueue'.", "default": "reject"}, "if_not_exists": {"type": "string", "enum": ["create", "reject"], "title": "If Not Exists", "description": "How to handle missing thread. Must be either 'reject' (raise error if missing), or 'create' (create new thread).", "default": "reject"}, "after_seconds": {"type": "number", "title": "After Seconds", "description": "The number of seconds to wait before starting the run. Use to schedule future runs."}, "checkpoint_during": {"type": "boolean", "title": "Checkpoint During", "description": "Whether to checkpoint during the run.", "default": false}}, "type": "object", "required": ["assistant_id"], "title": "RunCreateStateful", "description": "Payload for creating a run."}, "RunBatchCreate": {"type": "array", "items": {"$ref": "#/components/schemas/RunCreateStateless"}, "minItems": 1, "title": "RunBatchCreate", "description": "Payload for creating a batch of runs."}, "RunCreateStateless": {"properties": {"assistant_id": {"anyOf": [{"type": "string", "format": "uuid", "title": "Assistant Id"}, {"type": "string", "title": "Graph Id"}], "description": "The assistant ID or graph name to run. If using graph name, will default to first assistant created from that graph."}, "input": {"anyOf": [{"type": "object"}, {"type": "array"}, {"type": "string"}, {"type": "number"}, {"type": "boolean"}, {"type": "null"}], "title": "Input", "description": "The input to the graph."}, "command": {"anyOf": [{"$ref": "#/components/schemas/Command"}, {"type": "null"}], "title": "Input", "description": "The input to the graph."}, "metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> to assign to the run."}, "config": {"properties": {"tags": {"items": {"type": "string"}, "type": "array", "title": "Tags"}, "recursion_limit": {"type": "integer", "title": "Recursion Limit"}, "configurable": {"type": "object", "title": "Configurable"}}, "type": "object", "title": "Config", "description": "The configuration for the assistant."}, "webhook": {"type": "string", "maxLength": 65536, "minLength": 1, "format": "uri-reference", "title": "Webhook", "description": "Webhook to call after LangGraph API call is done."}, "interrupt_before": {"anyOf": [{"type": "string", "enum": ["*"]}, {"items": {"type": "string"}, "type": "array"}], "title": "Interrupt Before", "description": "Nodes to interrupt immediately before they get executed."}, "interrupt_after": {"anyOf": [{"type": "string", "enum": ["*"]}, {"items": {"type": "string"}, "type": "array"}], "title": "Interrupt After", "description": "Nodes to interrupt immediately after they get executed."}, "stream_mode": {"anyOf": [{"items": {"type": "string", "enum": ["values", "messages", "messages-tuple", "updates", "events", "debug", "custom"]}, "type": "array"}, {"type": "string", "enum": ["values", "messages", "messages-tuple", "updates", "events", "debug", "custom"]}], "title": "Stream Mode", "description": "The stream mode(s) to use.", "default": ["values"]}, "feedback_keys": {"items": {"type": "string"}, "type": "array", "title": "<PERSON><PERSON><PERSON>", "description": "Feedback keys to assign to run."}, "stream_subgraphs": {"type": "boolean", "title": "Stream Subgraphs", "description": "Whether to stream output from subgraphs.", "default": false}, "stream_resumable": {"type": "boolean", "title": "Stream Resumable", "description": "Whether to persist the stream chunks in order to resume the stream later.", "default": false}, "on_completion": {"type": "string", "enum": ["delete", "keep"], "title": "On Completion", "description": "Whether to delete or keep the thread created for a stateless run. Must be one of 'delete' or 'keep'.", "default": "delete"}, "on_disconnect": {"type": "string", "enum": ["cancel", "continue"], "title": "On Disconnect", "description": "The disconnect mode to use. Must be one of 'cancel' or 'continue'.", "default": "cancel"}, "after_seconds": {"type": "number", "title": "After Seconds", "description": "The number of seconds to wait before starting the run. Use to schedule future runs."}, "checkpoint_during": {"type": "boolean", "title": "Checkpoint During", "description": "Whether to checkpoint during the run.", "default": false}}, "type": "object", "required": ["assistant_id"], "title": "RunCreateStateless", "description": "Payload for creating a run."}, "AssistantSearchRequest": {"properties": {"metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>", "description": "Metadata to filter by. Exact match filter for each KV pair."}, "graph_id": {"type": "string", "title": "Graph Id", "description": "The ID of the graph to filter by. The graph ID is normally set in your langgraph.json configuration."}, "limit": {"type": "integer", "title": "Limit", "description": "The maximum number of results to return.", "default": 10, "minimum": 1, "maximum": 1000}, "offset": {"type": "integer", "title": "Offset", "description": "The number of results to skip.", "default": 0, "minimum": 0}, "sort_by": {"type": "string", "enum": ["assistant_id", "created_at", "updated_at", "name", "graph_id"], "title": "Sort By", "description": "The field to sort by."}, "sort_order": {"type": "string", "enum": ["asc", "desc"], "title": "Sort Order", "description": "The order to sort by."}}, "type": "object", "title": "AssistantSearchRequest", "description": "Payload for listing assistants."}, "AssistantVersionsSearchRequest": {"properties": {"metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>", "description": "Metadata to filter versions by. Exact match filter for each KV pair."}, "limit": {"type": "integer", "title": "Limit", "description": "The maximum number of versions to return.", "default": 10, "minimum": 1, "maximum": 1000}, "offset": {"type": "integer", "title": "Offset", "description": "The number of versions to skip.", "default": 0, "minimum": 0}}, "type": "object", "title": "SearchRequest", "description": "Payload for listing assistant versions."}, "ThreadSearchRequest": {"properties": {"metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>", "description": "Thread metadata to filter on."}, "values": {"type": "object", "title": "Values", "description": "State values to filter on."}, "status": {"type": "string", "enum": ["idle", "busy", "interrupted", "error"], "title": "Status", "description": "Thread status to filter on."}, "limit": {"type": "integer", "title": "Limit", "description": "Maximum number to return.", "default": 10, "minimum": 1, "maximum": 1000}, "offset": {"type": "integer", "title": "Offset", "description": "Offset to start from.", "default": 0, "minimum": 0}, "sort_by": {"type": "string", "enum": ["thread_id", "status", "created_at", "updated_at"], "title": "Sort By", "description": "Sort by field."}, "sort_order": {"type": "string", "enum": ["asc", "desc"], "title": "Sort Order", "description": "Sort order."}}, "type": "object", "title": "ThreadSearchRequest", "description": "Payload for listing threads."}, "Thread": {"properties": {"thread_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread."}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "The time the thread was created."}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "The last time the thread was updated."}, "metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>", "description": "The thread metadata."}, "status": {"type": "string", "enum": ["idle", "busy", "interrupted", "error"], "title": "Status", "description": "The status of the thread."}, "values": {"type": "object", "title": "Values", "description": "The current state of the thread."}}, "type": "object", "required": ["thread_id", "created_at", "updated_at", "metadata", "status"], "title": "<PERSON><PERSON><PERSON>"}, "ThreadCreate": {"properties": {"thread_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread. If not provided, a random UUID will be generated."}, "metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>", "description": "Metadata to add to thread."}, "if_exists": {"type": "string", "enum": ["raise", "do_nothing"], "title": "If Exists", "description": "How to handle duplicate creation. Must be either 'raise' (raise error if duplicate), or 'do_nothing' (return existing thread).", "default": "raise"}, "ttl": {"type": "object", "title": "TTL", "description": "The time-to-live for the thread.", "properties": {"strategy": {"type": "string", "enum": ["delete"], "description": "The TTL strategy. 'delete' removes the entire thread.", "default": "delete"}, "ttl": {"type": "number", "description": "The time-to-live in minutes from now until thread should be swept."}}}, "supersteps": {"type": "array", "items": {"type": "object", "properties": {"updates": {"type": "array", "items": {"$ref": "#/components/schemas/ThreadSuperstepUpdate"}}}, "required": ["updates"]}}}, "type": "object", "title": "ThreadCreate", "description": "Payload for creating a thread."}, "ThreadPatch": {"properties": {"metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>", "description": "Metadata to merge with existing thread metadata."}}, "type": "object", "title": "ThreadPatch", "description": "Payload for creating a thread."}, "ThreadStateCheckpointRequest": {"properties": {"checkpoint": {"$ref": "#/components/schemas/CheckpointConfig", "title": "Checkpoint", "description": "The checkpoint to get the state for."}, "subgraphs": {"type": "boolean", "title": "Subgraphs", "description": "Include subgraph states."}}, "required": ["checkpoint"], "type": "object", "title": "ThreadStateCheckpointRequest", "description": "Payload for getting the state of a thread at a checkpoint."}, "ThreadState": {"properties": {"values": {"anyOf": [{"items": {"type": "object"}, "type": "array"}, {"type": "object"}], "title": "Values"}, "next": {"items": {"type": "string"}, "type": "array", "title": "Next"}, "tasks": {"items": {"type": "object", "properties": {"id": {"type": "string", "title": "Task Id"}, "name": {"type": "string", "title": "Node Name"}, "error": {"type": "string", "title": "Error"}, "interrupts": {"type": "array", "items": {}}, "checkpoint": {"$ref": "#/components/schemas/CheckpointConfig", "title": "Checkpoint"}, "state": {"$ref": "#/components/schemas/ThreadState"}}, "required": ["id", "name"]}, "type": "array", "title": "Tasks"}, "checkpoint": {"$ref": "#/components/schemas/CheckpointConfig", "title": "Checkpoint"}, "metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>"}, "created_at": {"type": "string", "title": "Created At"}, "parent_checkpoint": {"type": "object", "title": "Parent Checkpoint"}}, "type": "object", "required": ["values", "next", "checkpoint", "metadata", "created_at"], "title": "ThreadState"}, "ThreadStateSearch": {"properties": {"limit": {"type": "integer", "title": "Limit", "description": "The maximum number of states to return.", "default": 10, "maximum": 1000, "minimum": 1}, "before": {"title": "Before", "description": "Return states before this checkpoint.", "$ref": "#/components/schemas/CheckpointConfig"}, "metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>", "description": "Filter states by metadata key-value pairs."}, "checkpoint": {"$ref": "#/components/schemas/CheckpointConfig", "title": "Checkpoint", "description": "Return states for this subgraph."}}, "type": "object", "title": "ThreadStateSearch"}, "ThreadStateUpdate": {"properties": {"values": {"anyOf": [{"items": {}, "type": "array"}, {"type": "object"}, {"type": "null"}], "title": "Values", "description": "The values to update the state with."}, "checkpoint": {"$ref": "#/components/schemas/CheckpointConfig", "title": "Checkpoint", "description": "The checkpoint to update the state of."}, "as_node": {"type": "string", "title": "As Node", "description": "Update the state as if this node had just executed."}}, "type": "object", "title": "ThreadStateUpdate", "description": "Payload for updating the state of a thread."}, "ThreadSuperstepUpdate": {"properties": {"values": {"anyOf": [{"type": "array", "items": {"type": "object"}}, {"type": "object"}, {"type": "null"}]}, "command": {"anyOf": [{"$ref": "#/components/schemas/Command"}, {"type": "null"}], "description": "The command associated with the update."}, "as_node": {"type": "string", "description": "Update the state as if this node had just executed."}}, "required": ["as_node"], "type": "object"}, "ThreadStateUpdateResponse": {"properties": {"checkpoint": {"type": "object", "title": "Checkpoint"}}, "type": "object", "title": "ThreadStateUpdateResponse", "description": "Response for adding state to a thread."}, "CheckpointConfig": {"type": "object", "title": "CheckpointConfig", "description": "Checkpoint config.", "properties": {"thread_id": {"type": "string", "description": "Unique identifier for the thread associated with this checkpoint."}, "checkpoint_ns": {"type": "string", "description": "Namespace for the checkpoint, used for organization and retrieval."}, "checkpoint_id": {"type": "string", "description": "Optional unique identifier for the checkpoint itself."}, "checkpoint_map": {"type": "object", "description": "Optional dictionary containing checkpoint-specific data."}}}, "StorePutRequest": {"type": "object", "required": ["namespace", "key", "value"], "properties": {"namespace": {"type": "array", "items": {"type": "string"}, "title": "Namespace", "description": "A list of strings representing the namespace path."}, "key": {"type": "string", "title": "Key", "description": "The unique identifier for the item within the namespace."}, "value": {"type": "object", "title": "Value", "description": "A dictionary containing the item's data."}}, "title": "StorePutRequest", "description": "Request to store or update an item."}, "StoreDeleteRequest": {"type": "object", "required": ["key"], "properties": {"namespace": {"type": "array", "items": {"type": "string"}, "title": "Namespace", "description": "A list of strings representing the namespace path."}, "key": {"type": "string", "title": "Key", "description": "The unique identifier for the item."}}, "title": "StoreDeleteRequest", "description": "Request to delete an item."}, "StoreSearchRequest": {"type": "object", "properties": {"namespace_prefix": {"type": ["array", "null"], "items": {"type": "string"}, "title": "Namespace Prefix", "description": "List of strings representing the namespace prefix."}, "filter": {"type": ["object", "null"], "additionalProperties": true, "title": "Filter", "description": "Optional dictionary of key-value pairs to filter results."}, "limit": {"type": "integer", "default": 10, "title": "Limit", "description": "Maximum number of items to return (default is 10)."}, "offset": {"type": "integer", "default": 0, "title": "Offset", "description": "Number of items to skip before returning results (default is 0)."}, "query": {"type": ["string", "null"], "title": "Query", "description": "Query string for semantic/vector search."}}, "title": "StoreSearchRequest", "description": "Request to search for items within a namespace prefix."}, "StoreListNamespacesRequest": {"type": "object", "properties": {"prefix": {"type": "array", "items": {"type": "string"}, "title": "Prefix", "description": "Optional list of strings representing the prefix to filter namespaces."}, "suffix": {"type": "array", "items": {"type": "string"}, "title": "Suffix", "description": "Optional list of strings representing the suffix to filter namespaces."}, "max_depth": {"type": "integer", "title": "<PERSON>", "description": "Optional integer specifying the maximum depth of namespaces to return."}, "limit": {"type": "integer", "default": 100, "title": "Limit", "description": "Maximum number of namespaces to return (default is 100)."}, "offset": {"type": "integer", "default": 0, "title": "Offset", "description": "Number of namespaces to skip before returning results (default is 0)."}}}, "Item": {"type": "object", "required": ["namespace", "key", "value", "created_at", "updated_at"], "properties": {"namespace": {"type": "array", "items": {"type": "string"}, "description": "The namespace of the item. A namespace is analogous to a document's directory."}, "key": {"type": "string", "description": "The unique identifier of the item within its namespace. In general, keys needn't be globally unique."}, "value": {"type": "object", "description": "The value stored in the item. This is the document itself."}, "created_at": {"type": "string", "format": "date-time", "description": "The timestamp when the item was created."}, "updated_at": {"type": "string", "format": "date-time", "description": "The timestamp when the item was last updated."}}, "description": "Represents a single document or data entry in the graph's Store. Items are used to store cross-thread memories."}, "RunsCancel": {"type": "object", "title": "RunsCancel", "description": "Payload for cancelling runs.", "properties": {"status": {"type": "string", "enum": ["pending", "running", "all"], "title": "Status", "description": "Filter runs by status to cancel. Must be one of 'pending', 'running', or 'all'."}, "thread_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON> Id", "description": "The ID of the thread containing runs to cancel."}, "run_ids": {"type": "array", "items": {"type": "string", "format": "uuid"}, "title": "Run Ids", "description": "List of run IDs to cancel."}}, "oneOf": [{"required": ["status"]}, {"required": ["thread_id", "run_ids"]}]}, "SearchItemsResponse": {"type": "object", "required": ["items"], "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Item"}}}}, "ListNamespaceResponse": {"type": "array", "items": {"type": "array", "items": {"type": "string"}}}, "ErrorResponse": {"type": "string", "title": "ErrorResponse", "description": "Error message returned from the server"}}, "responses": {"GetItemResponse": {"description": "Successful retrieval of an item.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Item"}}}}, "PutItemResponse": {"description": "Item successfully stored or updated.", "content": {}}, "DeleteItemResponse": {"description": "<PERSON>em successfully deleted.", "content": {}}, "SearchItemsResponse": {"description": "Successful search operation.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchItemsResponse"}}}}, "ListNamespacesResponse": {"description": "Successful retrieval of namespaces.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListNamespaceResponse"}}}}, "ErrorResponse": {"description": "An error occurred.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}