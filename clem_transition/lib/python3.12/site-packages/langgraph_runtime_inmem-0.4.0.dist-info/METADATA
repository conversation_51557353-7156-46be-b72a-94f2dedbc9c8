Metadata-Version: 2.4
Name: langgraph-runtime-inmem
Version: 0.4.0
Summary: Inmem implementation for the LangGraph API server.
Author-email: <PERSON> <<EMAIL>>
License: Elastic-2.0
Requires-Python: >=3.11.0
Requires-Dist: blockbuster<2.0.0,>=1.5.24
Requires-Dist: langgraph-checkpoint>=2.0.25
Requires-Dist: langgraph>=0.2
Requires-Dist: sse-starlette>=2
Requires-Dist: starlette>=0.37
Requires-Dist: structlog>23
Description-Content-Type: text/markdown

# LangGraph Runtime Inmem

This is the inmem implementation of the LangGraph Runtime API.

