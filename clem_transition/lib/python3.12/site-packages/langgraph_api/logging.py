import contextvars
import logging
import os
import threading
import typing

import structlog
from starlette.config import Config
from structlog.typing import EventDict

# env

log_env = Config()

LOG_JSON = log_env("LOG_JSON", cast=bool, default=False)
LOG_COLOR = log_env("LOG_COLOR", cast=bool, default=True)
LOG_LEVEL = log_env("LOG_LEVEL", cast=str, default="INFO")

logging.getLogger().setLevel(LOG_LEVEL.upper())
logging.getLogger("psycopg").setLevel(logging.WARNING)

worker_config: contextvars.ContextVar[dict[str, typing.Any] | None] = (
    contextvars.ContextVar("worker_config", default=None)
)

# custom processors


def add_thread_name(
    logger: logging.Logger, method_name: str, event_dict: EventDict
) -> EventDict:
    event_dict["thread_name"] = threading.current_thread().name
    return event_dict


def set_logging_context(val: dict[str, typing.Any] | None) -> contextvars.Token:
    if val is None:
        return worker_config.set(None)
    current = worker_config.get()
    if current is None:
        return worker_config.set(val)
    return worker_config.set({**current, **val})


class AddPrefixedEnvVars:
    def __init__(self, prefix: str) -> None:
        self.kv = {
            key.removeprefix(prefix).lower(): value
            for key, value in os.environ.items()
            if key.startswith(prefix)
        }

    def __call__(
        self, logger: logging.Logger, method_name: str, event_dict: EventDict
    ) -> EventDict:
        event_dict.update(self.kv)
        return event_dict


class AddApiVersion:
    def __call__(
        self, logger: logging.Logger, method_name: str, event_dict: EventDict
    ) -> EventDict:
        from langgraph_api import __version__

        event_dict["langgraph_api_version"] = __version__
        return event_dict


class AddLoggingContext:
    def __call__(
        self, logger: logging.Logger, method_name: str, event_dict: EventDict
    ) -> EventDict:
        if (ctx := worker_config.get()) is not None:
            event_dict.update(ctx)
        return event_dict


class JSONRenderer:
    def __call__(
        self, logger: logging.Logger, method_name: str, event_dict: EventDict
    ) -> str:
        """
        The return type of this depends on the return type of self._dumps.
        """
        from langgraph_api.serde import json_dumpb

        return json_dumpb(event_dict).decode()


LEVELS = logging.getLevelNamesMapping()


# shared config, for both logging and structlog

shared_processors = [
    add_thread_name,
    structlog.stdlib.add_logger_name,
    structlog.stdlib.add_log_level,
    structlog.stdlib.PositionalArgumentsFormatter(),
    structlog.stdlib.ExtraAdder(),
    AddPrefixedEnvVars("LANGSMITH_LANGGRAPH_"),  # injected by docker build
    AddApiVersion(),
    structlog.processors.TimeStamper(fmt="iso", utc=True),
    structlog.processors.StackInfoRenderer(),
    structlog.processors.format_exc_info,
    structlog.processors.UnicodeDecoder(),
    AddLoggingContext(),
]


# configure logging, used by logging.json, applied by uvicorn

renderer = (
    JSONRenderer() if LOG_JSON else structlog.dev.ConsoleRenderer(colors=LOG_COLOR)
)


class Formatter(structlog.stdlib.ProcessorFormatter):
    def __init__(self, *args, **kwargs) -> None:
        if len(args) == 3:
            fmt, datefmt, style = args
            kwargs["fmt"] = fmt
            kwargs["datefmt"] = datefmt
            kwargs["style"] = style
        else:
            raise RuntimeError("Invalid number of arguments")
        super().__init__(
            processors=[
                structlog.stdlib.ProcessorFormatter.remove_processors_meta,
                renderer,
            ],
            foreign_pre_chain=shared_processors,
            **kwargs,
        )


# configure structlog

if not structlog.is_configured():
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            *shared_processors,
            structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
        ],
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
